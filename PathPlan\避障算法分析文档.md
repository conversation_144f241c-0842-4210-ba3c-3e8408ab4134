# PathPlan避障算法分析文档

## 概述
PathPlan目录包含了完整的路径规划和避障算法实现，主要基于A*算法，并结合了多种避障策略来处理复杂的战场环境。

## 核心组件

### 1. PathPlanner类 (pathplanner.h/cpp)
**主要功能**：提供基于A*算法的路径规划和避障服务

#### 关键特性
- **多线程支持**：使用QThreadPool进行异步路径规划
- **共享地图**：通过SharedGridMap实现地图数据共享
- **线程安全**：使用QReadWriteLock保护地图访问
- **多种避障策略**：支持不同类型障碍物的差异化处理

### 2. 障碍物成本系统

#### 障碍物类型及成本配置
```cpp
// 当前成本配置
m_obstacleCosts[1] = 1.0f;       // 未探测区域 - 可通行，基本成本
m_obstacleCosts[2] = 500.0f;     // 雷达障碍物 - 高成本，尽量避开
m_obstacleCosts[3] = 999999.0f;  // 山体障碍物 - 不可通行
m_obstacleCosts[4] = 400.0f;     // 雷云(移动障碍物) - 较高成本
m_obstacleCosts[5] = 300.0f;     // 敌方无人机 - 高成本
m_obstacleCosts[6] = 400.0f;     // 雷云逃避区域 - 较高成本
```

#### 成本计算策略
- **基础移动成本**：直线移动1.0f，对角线移动1.414f
- **障碍物惩罚**：高成本区域使用平方关系增加权重
- **动态调整**：在障碍物中移动时降低惩罚系数

## 核心算法

### 1. A*路径规划算法

#### 算法流程
1. **起点终点验证**：检查起点和终点的有效性
2. **障碍物逃离**：如果起点在障碍物中，先寻找逃离点
3. **A*搜索**：使用优先队列进行最优路径搜索
4. **路径重建**：从终点回溯到起点构建完整路径
5. **备用方案**：如果A*失败，使用Bresenham直线算法

#### 关键方法
```cpp
QVector<QPoint> findPath(const QPoint& start, const QPoint& goal, const QString& droneId);
bool isValidPoint(int row, int col) const;
bool canMoveDiagonally(int startRow, int startCol, int endRow, int endCol) const;
float heuristic(const QPoint& a, const QPoint& b) const;
float getMovementCost(int row, int col) const;
```

### 2. 障碍物逃离算法

#### 逃离策略
- **BFS搜索**：使用广度优先搜索寻找最近的安全点
- **搜索范围限制**：最大搜索半径20格，防止无限搜索
- **8方向探索**：支持上下左右和对角线方向移动
- **距离优先**：优先选择距离起点最近的安全点

#### 实现细节
```cpp
QPoint findNearestNonObstaclePoint(const QPoint& start, const QString& droneId) const;
```

### 3. 两阶段路径规划

#### 阶段划分
1. **逃离阶段**：从障碍物中逃离到安全点
2. **导航阶段**：从安全点导航到目标点

#### 特殊处理
- **降低惩罚**：在障碍物中移动时降低成本惩罚
- **路径合并**：将两阶段路径合并为完整路径
- **容错机制**：如果无法完成两阶段，返回部分路径

## 避障策略特点

### 1. 多层次避障
- **硬避障**：山体等不可通行区域完全禁止
- **软避障**：雷达、雷云等高成本区域尽量避开
- **动态避障**：敌机位置实时更新，动态调整路径

### 2. 成本权重系统
- **基础成本**：1.0f（正常地形）
- **中等威胁**：300-500f（敌机、雷云、雷达）
- **极高威胁**：999999.0f（山体等不可通行）

### 3. 智能路径优化
- **欧几里得距离**：使用欧几里得距离作为启发式函数
- **对角线移动**：支持8方向移动，提高路径效率
- **成本平方惩罚**：对高成本区域使用平方关系增加避障效果

## 性能优化

### 1. 多线程处理
```cpp
// 异步路径规划
QtConcurrent::run(m_threadPool, [=]() {
    QVector<QPoint> path = findPath(start, goal, droneId);
    // 在主线程中发送结果信号
});
```

### 2. 内存优化
- **共享地图**：避免地图数据重复存储
- **路径缓存**：存储逃离路径避免重复计算
- **限制搜索**：设置搜索范围和路径长度限制

### 3. 线程安全
- **读写锁**：使用QReadWriteLock保护地图访问
- **信号槽机制**：通过Qt信号槽实现线程间通信

## 与策略系统的集成

### 1. Strategy4 - 人工势场法
- **势场避障**：使用引力和斥力计算避障向量
- **切向力**：添加切向力避免局部最优
- **多障碍物处理**：同时处理山体、雷达、雷云

### 2. Strategy2 - 智能适应
- **障碍物检测**：解析观察向量中的障碍物信息
- **距离计算**：计算到障碍物的距离用于决策
- **自适应避障**：根据环境复杂度调整避障策略

### 3. Strategy3 - 协同避障
- **团队路径规划**：为多个无人机规划协调路径
- **信息共享**：共享障碍物和路径信息
- **角色分工**：不同角色采用不同的避障策略

## 算法优势

### 1. 鲁棒性强
- **多重备案**：A*失败时使用直线规划
- **逃离机制**：处理起点在障碍物中的情况
- **容错设计**：各种异常情况都有处理方案

### 2. 适应性好
- **动态成本**：根据障碍物类型调整避障成本
- **实时更新**：支持地图实时更新和重新规划
- **多场景支持**：适用于不同复杂度的战场环境

### 3. 效率高
- **启发式搜索**：A*算法保证最优性和效率
- **多线程并行**：支持多个无人机同时路径规划
- **智能剪枝**：通过成本阈值减少搜索空间

## 改进建议

### 1. 算法增强
- **动态窗口法**：结合DWA算法处理动态障碍物
- **RRT*算法**：在复杂环境中使用快速随机树
- **预测避障**：预测移动障碍物的未来位置

### 2. 性能优化
- **分层路径规划**：粗糙路径+精细路径的两层规划
- **路径平滑**：使用样条曲线平滑路径
- **增量式重规划**：只重新计算受影响的路径段

### 3. 智能化提升
- **学习型避障**：从历史数据学习最优避障策略
- **协同优化**：多机协同的全局最优路径规划
- **风险评估**：基于概率的风险评估和路径选择

## 总结
PathPlan中的避障算法是一个功能完整、性能优良的路径规划系统，通过A*算法、成本权重系统、多线程处理等技术，实现了高效、鲁棒的避障功能，为无人机策略系统提供了可靠的路径规划服务。
