# 无人机策略差异化设计文档

## 概述
本文档详细说明了四个无人机策略的差异化定位和特色功能，确保每个策略在不同场景下都有明显的优势和区别。

## 策略1 - "闪电突击"策略

### 核心定位
**快速反应，高机动性的突击策略**

### 主要特色
- **极速响应**: 最快的决策速度，最小的计算延迟
- **高速机动**: 最大化速度利用（50.0f最大速度）
- **单点突破**: 专注单一目标，集中火力
- **优先目标**: 低血量敌机（快速清理残敌）

### 技术特点
```cpp
// 关键参数
const float LIGHTNING_MAX_VELOCITY = 50.0f;    // 最大突击速度
const float LIGHTNING_MIN_VELOCITY = 48.0f;    // 最小突击速度
const float LOW_HP_THRESHOLD = 30.0f;          // 低血量阈值

// 核心方法
QPointF calculateLightningStrikeVelocity();    // 快速计算攻击速度
int selectStrikeTarget();                      // 选择突击目标
QPointF calculateDirectAttackPath();           // 直线攻击路径
```

### 适用场景
- 敌机血量低，需要快速清理
- 开阔地形，障碍物少
- 需要快速支援队友
- 时间紧迫的战斗

---

## 策略2 - "智能适应"策略

### 核心定位
**自适应学习，复杂环境下的智能决策**

### 主要特色
- **环境适应**: 根据环境复杂度自动调整策略
- **学习能力**: 从历史数据中学习最优策略
- **复杂决策**: 处理多变量、多约束的复杂场景
- **混合策略**: 简单场景用规则，复杂场景用AI

### 技术特点
```cpp
// 核心功能
QPointF inference();                           // AI模型推理
QPointF customAvoidanceAndTracking();          // 自定义避障逻辑
int selectOptimalStrategy();                   // 策略选择
void adaptivePerformanceMonitoring();         // 性能监控
```

### 适用场景
- 障碍物密集的复杂环境
- 敌机行为复杂多变
- 需要智能决策的场景
- 长期作战需要学习适应

---

## 策略3 - "铁三角"策略

### 核心定位
**团队协同，阵型作战的集群策略**

### 主要特色
- **严密协同**: 三机联动，互相掩护
- **阵型优势**: 利用三角阵型压制敌机
- **角色分工**: 明确的攻击、支援、侦察分工
- **信息共享**: 实时共享敌机信息

### 技术特点
```cpp
// 阵型定义
const QMap<QString, QPoint> FORMATION_OFFSETS = {
    {"B1", QPoint(0, -60)},    // 蛇头/攻击手
    {"B2", QPoint(20, 0)},     // 蛇身/支援手
    {"B3", QPoint(-20, 60)}    // 蛇尾/侦察手
};

// 协同方法
void executeTriangleFormation();              // 三角阵型
void assignTriangleRoles();                   // 角色分配
void planCoordinatedAttack();                 // 协同攻击
void provideMutualCover();                    // 互相掩护
```

### 适用场景
- 敌机数量多，需要团队配合
- 需要控制战场区域
- 复杂的多目标作战
- 需要互相掩护的危险环境

---

## 策略4 - "全能战士"策略

### 核心定位
**平衡发展，适应性强的通用策略**

### 主要特色
- **攻防平衡**: 既能攻击又能防守
- **智能避障**: 优秀的障碍物处理能力
- **模式切换**: 根据战况灵活切换战术
- **环境适应**: 根据环境复杂度调整参数

### 技术特点
```cpp
// 平衡参数
const float ATTRACTIVE_FORCE = 1.5f;          // 平衡的引力
const float REPULSIVE_FORCE = 5500.0f;        // 平衡的斥力
const float MAX_VELOCITY = 48.0f;             // 平衡的速度

// 多模式支持
enum BehaviorMode {
    PATROL, PURSUE, INTERCEPT, EVADE, ESCAPE
};

// 适应性方法
float evaluateEnvironmentComplexity();        // 环境评估
void adaptParametersToEnvironment();          // 参数自适应
```

### 适用场景
- 通用场景，作为基准策略
- 环境变化较大的场景
- 需要平衡攻防的情况
- 作为其他策略的备选方案

---

## 策略对比表

| 特性 | 闪电突击 | 智能适应 | 铁三角 | 全能战士 |
|------|----------|----------|--------|----------|
| 速度 | ★★★★★ | ★★★☆☆ | ★★★☆☆ | ★★★★☆ |
| 避障 | ★☆☆☆☆ | ★★★★★ | ★★★☆☆ | ★★★★★ |
| 协同 | ★☆☆☆☆ | ★★☆☆☆ | ★★★★★ | ★★★☆☆ |
| 适应性 | ★★☆☆☆ | ★★★★★ | ★★☆☆☆ | ★★★★☆ |
| 计算复杂度 | ★☆☆☆☆ | ★★★★★ | ★★★★☆ | ★★★☆☆ |
| 鲁棒性 | ★★☆☆☆ | ★★★★☆ | ★★★☆☆ | ★★★★★ |

## 使用建议

### 场景选择指南
1. **开阔地形 + 残敌清理** → 选择闪电突击
2. **复杂环境 + 智能对手** → 选择智能适应  
3. **多敌机 + 团队作战** → 选择铁三角
4. **通用场景 + 稳定表现** → 选择全能战士

### 组合使用
- 可以根据战况动态切换策略
- 不同无人机可以使用不同策略形成混合编队
- 建议以全能战士为基础，特殊情况下切换专门策略

## 总结
通过差异化设计，四个策略现在具有明显的区分度和各自的优势领域，用户可以根据具体场景选择最适合的策略，或者组合使用多种策略以获得最佳效果。
