#include "mainwindow.h"
#include "ui_mainwindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    this->setWindowTitle("UAV控制终端");

    if (client.isInvalid()) {
        qDebug() << "Can not create VSOA client!";
        return; // 注意：这里不应该返回-1，因为构造函数没有返回值
    }

    // 正确连接信号和槽
    QObject::connect(&client, &QVsoaClient::connected,
                    this, &MainWindow::onConnected);
    QObject::connect(&client, &QVsoaClient::disconnected,
                    this, &MainWindow::onDisconnected);
    QObject::connect(&client, &QVsoaClient::datagram,
                    std::bind(&MainWindow::onDatagram, this, &client, _1, _2));

    // Connect to server with password
    client.connect2server("vsoa://127.0.0.1:3005/game_server", "", 1000);
    // Enable automatic connections
    client.autoConnect(1000, 500);
    // Subscribe /ctrl 订阅控制通道
    client.subscribe("/ctrl");
    // 订阅游戏数据通道
    client.subscribe("/game");
    // Enable data consistency on channels
    client.autoConsistent({"/ctrl", "/game"}, 1000);
    // 连接combobox选择变化信号
    connect(ui->id, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MainWindow::on_id_currentIndexChanged);

    // 设置UI控件
    ui->id->setFocusPolicy(Qt::NoFocus);

    // 初始化键盘计时器
    timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &MainWindow::joystickReturnedToCenter);
    timer->stop(); // 初始时计时器停止

    // 初始化控制命令发送计时器 (20Hz = 50ms)
    controlTimer = new QTimer(this);
    connect(controlTimer, &QTimer::timeout, this, &MainWindow::sendControlCommand);
    controlTimer->start(50); // 以20Hz的频率发送控制命令

    // 初始化摇杆参数
    radius = (height() - 220) / 6;
    gripWidth = (width() - 200) / 4;
    gripHeight = (height() - 220) / 3;
    gripY = height() - 200;

    // 初始化雷达扫描计时器
    radarSweepTimer = new QTimer(this);
    connect(radarSweepTimer, &QTimer::timeout, this, &MainWindow::updateRadarSweep);
    radarSweepTimer->start(100); // 100ms更新一次雷达扫描

    // 计算六个区域的位置
    calculateRegions();
}

MainWindow::~MainWindow()
{
    // 停止计时器
    if (timer->isActive()) {
        timer->stop();
    }
    if (controlTimer->isActive()) {
        controlTimer->stop();
    }
    if (radarSweepTimer->isActive()) {
        radarSweepTimer->stop();
    }
    delete ui;
}

void MainWindow::paintEvent(QPaintEvent *event) {
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // 重新计算区域（以防窗口大小改变）
    calculateRegions();

    // 绘制遥控器背景
    painter.setPen(QPen(Qt::black, 3));
    painter.setBrush(QColor(50, 50, 50));
    painter.drawRoundedRect(20, 20, width() - 40, height() - 40, 20, 20);

    // 绘制六个功能区域
    drawFriendlyInfoRegion(painter);
    drawRadarRegion(painter);
    drawEnemyInfoRegion(painter);
    drawJoystickRegion(painter);
    drawStatusRegion(painter);
    drawCompassRegion(painter);
}

void MainWindow::mousePressEvent(QMouseEvent *event) {
    // 检查是否点击在摇杆区域
    QPoint joystickCenter(regions.joystickRegion.center().x(), regions.joystickRegion.center().y() + 10);
    int joystickRadius = qMin(regions.joystickRegion.width(), regions.joystickRegion.height()) / 3;

    if (QLineF(joystickCenter, event->pos()).length() <= joystickRadius) {
        dragging = true;
        updateJoystickPosition(event->pos());
        return;
    }

    // 检查是否点击在无人机选择按钮上
    QStringList uavList = {"B1", "B2", "B3"};
    int buttonWidth = 30;
    int buttonSpacing = 5;
    int startX = regions.statusRegion.x() + 10;
    int buttonY = regions.statusRegion.y() + 55;

    for (int i = 0; i < uavList.size(); ++i) {
        QRect buttonRect(startX + i * (buttonWidth + buttonSpacing), buttonY, buttonWidth, 20);
        if (buttonRect.contains(event->pos())) {
            currentSelectedUAV = uavList[i];
            currentDroneId = i + 1;
            updateUIDisplay();
            update(); // 重绘界面
            return;
        }
    }
}

// 在鼠标移动事件中，计算并更新角度，只在角度改变时发送信号
void MainWindow::mouseMoveEvent(QMouseEvent *event) {
    if (dragging) {
        updateJoystickPosition(event->pos());  // 更新摇杆位置

        int newAngle = getJoystickAngle();  // 获取新的角度

        // 如果角度发生变化
        if (newAngle != lastAngle) {
            lastAngle = newAngle;  // 更新最后的角度
        }
    }
}

void MainWindow::updateJoystickPosition(const QPointF &pos) {
    QPoint joystickCenter(regions.joystickRegion.center().x(), regions.joystickRegion.center().y() + 10);
    int joystickRadius = qMin(regions.joystickRegion.width(), regions.joystickRegion.height()) / 3;

    joystickPos = pos - joystickCenter;

    // 限制摇杆在圆内
    if (QLineF(QPointF(0, 0), joystickPos).length() > joystickRadius) {
        QLineF line(QPointF(0, 0), joystickPos);
        line.setLength(joystickRadius);
        joystickPos = line.p2();
    }

    // 更新radius用于角度计算
    radius = joystickRadius;

    // 只重绘摇杆区域
    update(regions.joystickRegion);
}

void MainWindow::mouseReleaseEvent(QMouseEvent *event) {
    dragging = false;
    joystickPos = QPointF(0, 0);
    lastAngle = -1;
    qDebug() << "摇杆返回中心！";

    // 只重绘摇杆区域
    update(regions.joystickRegion);
}

void MainWindow::joystickReturnedToCenter() {
    keyPressCount = 0;
    timer->stop();

    joystickPos = QPointF(0, 0);
    lastAngle = -1;
    
    // 只重绘摇杆区域
    update(regions.joystickRegion);
}

void MainWindow::keyPressEvent(QKeyEvent *event) {
    constexpr float step = 5.0f;
    switch (event->key()) {
       case Qt::Key_Up:
           joystickPos.setY(joystickPos.y() - step);
           break;
       case Qt::Key_Down:
           joystickPos.setY(joystickPos.y() + step);
           break;
       case Qt::Key_Left:
           joystickPos.setX(joystickPos.x() - step);
           break;
       case Qt::Key_Right:
           joystickPos.setX(joystickPos.x() + step);
           break;
       default:
           return;
    }

    // 保证摇杆指针始终保持在圆内
    if (QLineF(0, 0, joystickPos.x(), joystickPos.y()).length() > radius) {
        QLineF line(QPointF(0, 0), joystickPos);
        line.setLength(radius);
        joystickPos = line.p2();
    }

    int newAngle = getJoystickAngle();

    if(joystickPos == QPointF(0, 0)) {
        lastAngle = -1;
    } else if (newAngle != lastAngle) {
        qDebug() << "角度变化：" << lastAngle << "->" << newAngle;
        lastAngle = newAngle;
    }

    // 只重绘摇杆区域
    QPointF joystickCenter(width()/ 2, gripY - radius - 30);
    QRect updateRect(joystickCenter.x() - radius - 15, 
                     joystickCenter.y() - radius - 15, 
                     (radius + 15) * 2, 
                     (radius + 15) * 2);
    update(updateRect);
    
    // 开始计时，或重置计时器
    if (keyPressCount == 0) {
        timer->start(1000); // 启动计时器
    } else {
        timer->stop();
        timer->start(1000); // 重启计时器
    }

    keyPressCount++;
}

int MainWindow::getJoystickAngle() const {                // 计算摇杆指针的角度（返回值为角度）
    if (joystickPos.isNull()) {
        return -1;  // 中心位置
    }

    // 计算角度（0度为向右，90度为向下）
    double angle = qAtan2(joystickPos.y(), joystickPos.x()) * 180 / M_PI;
    return static_cast<int>(angle);
}

// 处理combobox选择变化
void MainWindow::on_id_currentIndexChanged(int index)
{
    // 根据选择的索引设置当前无人机ID
    // B1对应ID=1，B2对应ID=2，B3对应ID=3
    currentDroneId = index + 1;

//    qDebug() << "当前选择的无人机ID：" << currentDroneId;

    // 更新UI显示
    updateUIDisplay();
}

void MainWindow::onConnected(bool ok, QString info)
{
    if (!ok) {
        qDebug() << "Connected with server failed!";
        return;
    }
    qDebug() << "Connected with server:" << info;
}

void MainWindow::onDisconnected()
{
    qDebug() << "Connection break";
}

void MainWindow::onDatagram(QVsoaClient *client, QString url, QVsoaPayload payload)
{
//    qDebug() << "Datagram received from URL" << url << "with payload:" << payload.param();

    QVariant param = payload.param();
    // 处理接收到的数据
    if (url == "/game") {
        // 处理游戏数据
        QString dataStr;

        // 根据实际类型转换
        if (param.type() == QVariant::String) {
            dataStr = param.toString();
        } else if (param.canConvert<QByteArray>()) {
            dataStr = QString::fromUtf8(param.toByteArray());
        } else {
            qDebug() << "不支持的参数类型:" << param.typeName();
            return;
        }

        processGameData(dataStr);
    } else if (url == "/ctrl") {
        // 处理控制数据的反馈
        qDebug() << "接收到控制数据反馈：" << param;
    }
}


void MainWindow::processGameData(const QString &jsonData)         // 处理服务器发来的游戏数据
{
    QJsonDocument doc = QJsonDocument::fromJson(jsonData.toUtf8());
    if (doc.isNull() || !doc.isObject()) {
        qDebug() << "无效的JSON数据";
        return;
    }

    QJsonObject gameObj = doc.object();

    // 更新游戏状态
    if (gameObj.contains("left_time")) {
        gameLeftTime = gameObj["left_time"].toInt();
    }

    if (gameObj.contains("stage")) {
        gameStage = gameObj["stage"].toString();
    }

    // 更新无人机信息
    if (gameObj.contains("drones") && gameObj["drones"].isArray()) {
        QJsonArray drones = gameObj["drones"].toArray();

        // 保存当前选择的无人机ID
        QString currentUid = ui->id->currentText();

        // 记录当前选择的无人机信息（如果存在）
        DroneInfo oldInfo;
        bool hadInfo = dronesInfo.contains(currentUid);
        if (hadInfo) {
            oldInfo = dronesInfo[currentUid];
        }

        // 清空之前的数据
        dronesInfo.clear();

        // 更新无人机信息
        for (const QJsonValue &droneValue : drones) {
            if (droneValue.isObject()) {
                QJsonObject drone = droneValue.toObject();

                DroneInfo info;
                info.hp = drone["hp"].toInt();
                info.status = drone["status"].toString();
                info.team = drone["team"].toString();
                info.uid = drone["uid"].toString();
                info.x = drone["x"].toDouble();
                info.y = drone["y"].toDouble();

                // vx和vy可能不存在于所有无人机数据中
                if (drone.contains("vx")) {
                    info.vx = drone["vx"].toDouble();
                }

                if (drone.contains("vy")) {
                    info.vy = drone["vy"].toDouble();
                }

                // 存储无人机信息
                dronesInfo[info.uid] = info;
            }
        }
        // 只有当前选择的无人机信息发生变化时才更新UI
        bool needUpdate = !hadInfo || !dronesInfo.contains(currentUid) ||
                         (dronesInfo[currentUid].hp != oldInfo.hp) ||
                         (dronesInfo[currentUid].x != oldInfo.x) ||
                         (dronesInfo[currentUid].y != oldInfo.y) ||
                         (dronesInfo[currentUid].status != oldInfo.status);

        if (needUpdate) {
            updateUIDisplay();
        }
    }

    // 更新障碍物信息
    if (gameObj.contains("obstacles") && gameObj["obstacles"].isArray()) {
        QJsonArray obstacles = gameObj["obstacles"].toArray();

        // 清空之前的障碍物数据
        obstaclesInfo.clear();

        // 更新障碍物信息
        for (const QJsonValue &obstacleValue : obstacles) {
            if (obstacleValue.isObject()) {
                QJsonObject obstacle = obstacleValue.toObject();

                ObstacleInfo info;
                info.id = obstacle["id"].toString();
                info.r = obstacle["r"].toDouble();
                info.type = obstacle["type"].toString();
                info.x = obstacle["x"].toDouble();
                info.y = obstacle["y"].toDouble();

                // 存储障碍物信息
                obstaclesInfo[info.id] = info;
            }
        }
    }

    // 更新UI显示
    updateUIDisplay();
}


void MainWindow::updateUIDisplay()          // 更新遥控器UI显示
{
    // 获取当前选择的无人机ID
    QString currentUid = ui->id->currentText();
    currentSelectedUAV = currentUid;

    // 如果存在该无人机的信息，则更新UI
    if (dronesInfo.contains(currentUid)) {
        DroneInfo &info = dronesInfo[currentUid];

        // 更新血量
        ui->hp->setValue(info.hp);

        // 更新坐标
        ui->point->setText(QString("(%1, %2)").arg(info.x, 0, 'f', 1).arg(info.y, 0, 'f', 1));

        // 计算当前速度
        currentSpeed = sqrt(info.vx * info.vx + info.vy * info.vy);

        // 更新飞行时间（简化计算，实际应该累计）
        static QMap<QString, QDateTime> startTimes;
        if (!startTimes.contains(currentUid)) {
            startTimes[currentUid] = QDateTime::currentDateTime();
        }
        flightTime = startTimes[currentUid].secsTo(QDateTime::currentDateTime());

        // 更新飞行距离（简化计算，实际应该累计路径长度）
        static QMap<QString, QPointF> lastPositions;
        if (lastPositions.contains(currentUid)) {
            QPointF lastPos = lastPositions[currentUid];
            QPointF currentPos(info.x, info.y);
            float deltaDistance = QLineF(lastPos, currentPos).length();
            flightDistance += deltaDistance;
        }
        lastPositions[currentUid] = QPointF(info.x, info.y);

        // 如果无人机已坠毁，显示提示
        if (info.status == "down") {
            ui->hp->setValue(0);
            currentSpeed = 0.0f;
        }
    } else {
        // 如果没有该无人机的信息，清空显示
        ui->hp->setValue(0);
        currentSpeed = 0.0f;

        // 对于敌方无人机，显示特殊信息
        if (currentUid.startsWith("R")) {
            ui->point->setText("未知 ");
        } else {
            ui->point->setText("(0, 0)");
        }
    }

    // 设置状态栏文本颜色和字体大小
    statusBar()->showMessage(QString("游戏状态: %1, 剩余时间: %2秒").arg(gameStage).arg(gameLeftTime));

    // 重绘界面以更新所有区域
    update();
}


void MainWindow::sendControlCommand()            // 发送控制命令到服务器
{
    // 获取当前角度
    int angle = getJoystickAngle();

    // 如果摇杆在中心位置，发送速度为0的命令
    double vx = 0.0;
    double vy = 0.0;

    if (angle != -1) {
            // 将角度转换为弧度
            double radians = angle * M_PI / 180.0;

            // 计算摇杆距离中心的比例 (0.0 到 1.0)
            double distanceRatio = QLineF(QPointF(0, 0), joystickPos).length() / radius;

            // 计算初始vx和vy
            double rawVx = qCos(radians);
            double rawVy = qSin(radians);

            // 计算缩放因子，确保vx和vy的最大值为50
            double maxComponent = qMax(qAbs(rawVx), qAbs(rawVy));
            double scaleFactor = 50.0 / maxComponent;

            // 应用缩放因子和距离比例
            vx = rawVx * scaleFactor * distanceRatio;
            vy = rawVy * scaleFactor * distanceRatio;
        }

    // 获取当前选择的无人机ID文本
    QString droneId = ui->id->currentText();

    // 创建JSON对象
    QJsonObject controlObj;
    controlObj["uid"] = droneId;  // 直接使用下拉框中的文本
    controlObj["vx"] = vx;
    controlObj["vy"] = vy;

    // 转换为JSON文档
    QJsonDocument doc(controlObj);
    QString jsonString = doc.toJson(QJsonDocument::Compact);

    // 创建payload并发送
    QVsoaPayload payload;
    payload.setParam(jsonString);

    // 发送到/ctrl通道
    client.sendDatagram("/ctrl", payload);

//    // 调试输出
//    if (angle != -1) {
//        qDebug() << "发送控制命令: uid=" << droneId
//                 << ", vx=" << vx << ", vy=" << vy
//                 << ", 角度=" << angle;
//    }
}

// 计算六个区域的位置
void MainWindow::calculateRegions() {
    int margin = 30;
    int regionWidth = (width() - 4 * margin) / 3;
    int regionHeight = (height() - 3 * margin) / 2;

    // 上排三个区域
    regions.friendlyInfoRegion = QRect(margin, margin, regionWidth, regionHeight);
    regions.radarRegion = QRect(margin * 2 + regionWidth, margin, regionWidth, regionHeight);
    regions.enemyInfoRegion = QRect(margin * 3 + regionWidth * 2, margin, regionWidth, regionHeight);

    // 下排三个区域
    regions.joystickRegion = QRect(margin, margin * 2 + regionHeight, regionWidth, regionHeight);
    regions.statusRegion = QRect(margin * 2 + regionWidth, margin * 2 + regionHeight, regionWidth, regionHeight);
    regions.compassRegion = QRect(margin * 3 + regionWidth * 2, margin * 2 + regionHeight, regionWidth, regionHeight);
}

// 绘制我方信息区域（左上）
void MainWindow::drawFriendlyInfoRegion(QPainter &painter) {
    QRect region = regions.friendlyInfoRegion;

    // 绘制区域边框
    painter.setPen(QPen(Qt::cyan, 2));
    painter.setBrush(QColor(0, 50, 100, 100));
    painter.drawRoundedRect(region, 10, 10);

    // 标题
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 12, QFont::Bold));
    painter.drawText(region.x() + 10, region.y() + 20, "我方信息");

    // 绘制我方无人机信息
    painter.setFont(QFont("Arial", 10));
    int yOffset = 40;

    QStringList friendlyUAVs = {"B1", "B2", "B3"};
    for (const QString &uavId : friendlyUAVs) {
        if (dronesInfo.contains(uavId)) {
            const DroneInfo &info = dronesInfo[uavId];

            // 高亮当前选择的无人机
            if (uavId == currentSelectedUAV) {
                painter.setPen(Qt::yellow);
                painter.setBrush(QColor(255, 255, 0, 50));
                painter.drawRect(region.x() + 5, region.y() + yOffset - 15, region.width() - 10, 20);
            }

            painter.setPen(Qt::white);
            QString text = QString("%1: HP:%2 (%3,%4)")
                          .arg(uavId)
                          .arg(info.hp)
                          .arg(QString::number(info.x, 'f', 1))
                          .arg(QString::number(info.y, 'f', 1));
            painter.drawText(region.x() + 10, region.y() + yOffset, text);

            // 绘制血量条
            QRect hpBar(region.x() + 10, region.y() + yOffset + 5, region.width() - 20, 8);
            painter.setPen(Qt::gray);
            painter.setBrush(Qt::darkGray);
            painter.drawRect(hpBar);

            // 血量填充
            QColor hpColor = info.hp > 60 ? Qt::green : (info.hp > 30 ? Qt::yellow : Qt::red);
            painter.setBrush(hpColor);
            int hpWidth = (hpBar.width() * info.hp) / 100;
            painter.drawRect(hpBar.x(), hpBar.y(), hpWidth, hpBar.height());

            yOffset += 35;
        }
    }
}

// 绘制雷达区域（中上）
void MainWindow::drawRadarRegion(QPainter &painter) {
    QRect region = regions.radarRegion;

    // 绘制区域边框
    painter.setPen(QPen(Qt::green, 2));
    painter.setBrush(QColor(0, 100, 0, 100));
    painter.drawRoundedRect(region, 10, 10);

    // 标题
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 12, QFont::Bold));
    painter.drawText(region.x() + 10, region.y() + 20, "雷达探测");

    // 绘制雷达圆形显示
    QPoint radarCenter(region.center().x(), region.center().y() + 10);
    int radarRadius = qMin(region.width(), region.height()) / 3;

    // 雷达背景圆
    painter.setPen(QPen(Qt::green, 1));
    painter.setBrush(QColor(0, 50, 0, 150));
    painter.drawEllipse(radarCenter, radarRadius, radarRadius);

    // 绘制探测范围指示圆圈（300像素探测范围）
    painter.setPen(QPen(Qt::cyan, 1, Qt::DashLine));
    painter.setBrush(Qt::NoBrush);
    painter.drawEllipse(radarCenter.x() - radarRadius, radarCenter.y() - radarRadius,
                       radarRadius * 2, radarRadius * 2);

    // 添加探测范围标识
    painter.setPen(Qt::cyan);
    painter.setFont(QFont("Arial", 8));
    painter.drawText(radarCenter.x() + radarRadius - 30, radarCenter.y() - radarRadius + 15, "300px");

    // 雷达网格线
    painter.setPen(QPen(Qt::green, 1, Qt::DotLine));
    painter.drawLine(radarCenter.x() - radarRadius, radarCenter.y(),
                     radarCenter.x() + radarRadius, radarCenter.y());
    painter.drawLine(radarCenter.x(), radarCenter.y() - radarRadius,
                     radarCenter.x(), radarCenter.y() + radarRadius);

    // 雷达扫描线
    painter.setPen(QPen(Qt::yellow, 2));
    float sweepX = radarCenter.x() + radarRadius * cos(radarSweepAngle * M_PI / 180.0f);
    float sweepY = radarCenter.y() + radarRadius * sin(radarSweepAngle * M_PI / 180.0f);
    painter.drawLine(radarCenter, QPointF(sweepX, sweepY));

    // 绘制探测到的目标
    // 无人机探测参数：半径300像素，360度全方位探测
    if (dronesInfo.contains(currentSelectedUAV)) {
        const DroneInfo &currentDrone = dronesInfo[currentSelectedUAV];

        // 绘制敌方目标
        QStringList enemyUAVs = {"R1", "R2", "R3"};
        for (const QString &enemyId : enemyUAVs) {
            if (dronesInfo.contains(enemyId)) {
                const DroneInfo &enemy = dronesInfo[enemyId];

                // 计算相对位置
                float dx = enemy.x - currentDrone.x;
                float dy = enemy.y - currentDrone.y;
                float distance = sqrt(dx*dx + dy*dy);

                // 只显示探测范围内的目标（300像素）
                if (distance < 300.0f) {
                    float scale = radarRadius / 300.0f;
                    int targetX = radarCenter.x() + dx * scale;
                    int targetY = radarCenter.y() + dy * scale;

                    painter.setPen(Qt::red);
                    painter.setBrush(Qt::red);
                    painter.drawEllipse(targetX - 3, targetY - 3, 6, 6);

                    // 显示目标ID
                    painter.setPen(Qt::white);
                    painter.setFont(QFont("Arial", 8));
                    painter.drawText(targetX + 5, targetY - 5, enemyId);
                }
            }
        }

        // 绘制障碍物
        for (auto it = obstaclesInfo.begin(); it != obstaclesInfo.end(); ++it) {
            const ObstacleInfo &obstacle = it.value();

            // 计算相对位置
            float dx = obstacle.x - currentDrone.x;
            float dy = obstacle.y - currentDrone.y;
            float distance = sqrt(dx*dx + dy*dy);

            // 只显示探测范围内的障碍物（300像素）
            if (distance < 300.0f) {
                float scale = radarRadius / 300.0f;
                int obsX = radarCenter.x() + dx * scale;
                int obsY = radarCenter.y() + dy * scale;
                int obsRadius = obstacle.r * scale;

                // 根据障碍物类型设置颜色
                QColor obstacleColor;
                QString obstacleSymbol;
                if (obstacle.type == "mountain") {
                    obstacleColor = Qt::darkGreen;
                    obstacleSymbol = "M";
                } else if (obstacle.type == "radar") {
                    obstacleColor = Qt::magenta;
                    obstacleSymbol = "R";
                } else if (obstacle.type == "cloud") {
                    obstacleColor = Qt::yellow;
                    obstacleSymbol = "C";
                }

                // 绘制障碍物圆形
                painter.setPen(QPen(obstacleColor, 2));
                painter.setBrush(QColor(obstacleColor.red(), obstacleColor.green(), obstacleColor.blue(), 100));
                painter.drawEllipse(obsX - obsRadius, obsY - obsRadius, obsRadius * 2, obsRadius * 2);

                // 显示障碍物标识
                painter.setPen(Qt::white);
                painter.setFont(QFont("Arial", 8, QFont::Bold));
                painter.drawText(obsX - 5, obsY + 3, obstacleSymbol);

                // 显示障碍物ID
                painter.setFont(QFont("Arial", 7));
                painter.drawText(obsX + obsRadius + 2, obsY - obsRadius, obstacle.id);
            }
        }
    }
}

// 绘制敌方信息区域（右上）
void MainWindow::drawEnemyInfoRegion(QPainter &painter) {
    QRect region = regions.enemyInfoRegion;

    // 绘制区域边框
    painter.setPen(QPen(Qt::red, 2));
    painter.setBrush(QColor(100, 0, 0, 100));
    painter.drawRoundedRect(region, 10, 10);

    // 标题
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 12, QFont::Bold));
    painter.drawText(region.x() + 10, region.y() + 20, "敌方信息");

    // 绘制敌方无人机信息
    painter.setFont(QFont("Arial", 10));
    int yOffset = 40;

    QStringList enemyUAVs = {"R1", "R2", "R3"};
    for (const QString &uavId : enemyUAVs) {
        if (dronesInfo.contains(uavId)) {
            const DroneInfo &info = dronesInfo[uavId];

            painter.setPen(Qt::white);
            QString text = QString("%1: HP:%2 (%3,%4)")
                          .arg(uavId)
                          .arg(info.hp)
                          .arg(QString::number(info.x, 'f', 1))
                          .arg(QString::number(info.y, 'f', 1));
            painter.drawText(region.x() + 10, region.y() + yOffset, text);

            // 绘制血量条
            QRect hpBar(region.x() + 10, region.y() + yOffset + 5, region.width() - 20, 8);
            painter.setPen(Qt::gray);
            painter.setBrush(Qt::darkGray);
            painter.drawRect(hpBar);

            // 血量填充（敌方用红色系）
            QColor hpColor = info.hp > 60 ? Qt::darkRed : (info.hp > 30 ? QColor(200, 100, 0) : QColor(100, 0, 0));
            painter.setBrush(hpColor);
            int hpWidth = (hpBar.width() * info.hp) / 100;
            painter.drawRect(hpBar.x(), hpBar.y(), hpWidth, hpBar.height());

            // 计算距离（如果当前选择的无人机存在）
            if (dronesInfo.contains(currentSelectedUAV)) {
                const DroneInfo &currentDrone = dronesInfo[currentSelectedUAV];
                float dx = info.x - currentDrone.x;
                float dy = info.y - currentDrone.y;
                float distance = sqrt(dx*dx + dy*dy);

                painter.setPen(Qt::lightGray);
                painter.setFont(QFont("Arial", 8));
                QString distText = QString("距离: %1").arg(QString::number(distance, 'f', 1));
                painter.drawText(region.x() + 10, region.y() + yOffset + 20, distText);
            }

            yOffset += 45;
        }
    }

    // 显示最近的威胁障碍物
    if (dronesInfo.contains(currentSelectedUAV)) {
        const DroneInfo &currentDrone = dronesInfo[currentSelectedUAV];

        // 找到最近的威胁障碍物
        QString nearestThreat;
        float minDistance = 999999.0f;
        QString threatType;

        for (auto it = obstaclesInfo.begin(); it != obstaclesInfo.end(); ++it) {
            const ObstacleInfo &obstacle = it.value();
            float dx = obstacle.x - currentDrone.x;
            float dy = obstacle.y - currentDrone.y;
            float distance = sqrt(dx*dx + dy*dy);

            // 只考虑一定范围内的威胁
            if (distance < 300.0f && distance < minDistance) {
                minDistance = distance;
                nearestThreat = obstacle.id;
                threatType = obstacle.type;
            }
        }

        // 显示最近威胁信息
        if (!nearestThreat.isEmpty()) {
            painter.setPen(Qt::yellow);
            painter.setFont(QFont("Arial", 9, QFont::Bold));
            painter.drawText(region.x() + 10, region.y() + yOffset, "最近威胁:");

            yOffset += 15;
            painter.setPen(Qt::white);
            painter.setFont(QFont("Arial", 8));
            QString threatText = QString("%1 (%2)").arg(nearestThreat).arg(threatType);
            painter.drawText(region.x() + 10, region.y() + yOffset, threatText);

            yOffset += 12;
            QString distText = QString("距离: %1").arg(QString::number(minDistance, 'f', 1));
            painter.drawText(region.x() + 10, region.y() + yOffset, distText);
        }
    }
}

// 绘制摇杆控制区域（左下）
void MainWindow::drawJoystickRegion(QPainter &painter) {
    QRect region = regions.joystickRegion;

    // 绘制区域边框
    painter.setPen(QPen(Qt::yellow, 2));
    painter.setBrush(QColor(100, 100, 0, 100));
    painter.drawRoundedRect(region, 10, 10);

    // 标题
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 12, QFont::Bold));
    painter.drawText(region.x() + 10, region.y() + 20, "飞行控制");

    // 计算摇杆位置和大小
    QPoint joystickCenter(region.center().x(), region.center().y() + 10);
    int joystickRadius = qMin(region.width(), region.height()) / 3;

    // 绘制摇杆背景圆
    painter.setPen(QPen(Qt::darkGray, 2));
    painter.setBrush(QColor(240, 240, 240, 150));
    painter.drawEllipse(joystickCenter, joystickRadius, joystickRadius);

    // 绘制方向指示线
    painter.setPen(QPen(Qt::gray, 1, Qt::DotLine));
    painter.drawLine(joystickCenter.x() - joystickRadius, joystickCenter.y(),
                     joystickCenter.x() + joystickRadius, joystickCenter.y());
    painter.drawLine(joystickCenter.x(), joystickCenter.y() - joystickRadius,
                     joystickCenter.x(), joystickCenter.y() + joystickRadius);

    // 绘制摇杆指针
    painter.setBrush(Qt::darkBlue);
    painter.setPen(QPen(Qt::blue, 2));

    // 计算摇杆指针位置（相对于摇杆中心）
    QPointF scaledJoystickPos = joystickPos * (joystickRadius / radius);
    QPointF knobPos = joystickCenter + scaledJoystickPos;
    painter.drawEllipse(knobPos, 12, 12);

    // 显示摇杆状态
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 9));
    QString statusText;
    if (joystickPos.isNull()) {
        statusText = "中心位置";
    } else {
        int angle = getJoystickAngle();
        float magnitude = QLineF(QPointF(0, 0), joystickPos).length() / radius;
        statusText = QString("角度:%1° 强度:%2%").arg(angle).arg(QString::number(magnitude * 100, 'f', 0));
    }
    painter.drawText(region.x() + 10, region.bottom() - 10, statusText);
}

// 绘制状态信息区域（中下）
void MainWindow::drawStatusRegion(QPainter &painter) {
    QRect region = regions.statusRegion;

    // 绘制区域边框
    painter.setPen(QPen(Qt::magenta, 2));
    painter.setBrush(QColor(100, 0, 100, 100));
    painter.drawRoundedRect(region, 10, 10);

    // 标题
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 12, QFont::Bold));
    painter.drawText(region.x() + 10, region.y() + 20, "状态信息");

    // 无人机选择
    painter.setFont(QFont("Arial", 11, QFont::Bold));
    painter.setPen(Qt::yellow);
    painter.drawText(region.x() + 10, region.y() + 45, QString("当前控制: %1").arg(currentSelectedUAV));

    // 绘制无人机选择按钮
    painter.setFont(QFont("Arial", 9));
    painter.setPen(Qt::white);
    QStringList uavList = {"B1", "B2", "B3"};
    int buttonWidth = 30;
    int buttonSpacing = 5;
    int startX = region.x() + 10;
    int buttonY = region.y() + 55;

    for (int i = 0; i < uavList.size(); ++i) {
        QString uavId = uavList[i];
        QRect buttonRect(startX + i * (buttonWidth + buttonSpacing), buttonY, buttonWidth, 20);

        // 高亮当前选择的无人机
        if (uavId == currentSelectedUAV) {
            painter.setPen(QPen(Qt::yellow, 2));
            painter.setBrush(QColor(255, 255, 0, 100));
        } else {
            painter.setPen(QPen(Qt::gray, 1));
            painter.setBrush(QColor(50, 50, 50, 100));
        }

        painter.drawRoundedRect(buttonRect, 3, 3);
        painter.setPen(Qt::white);
        painter.drawText(buttonRect, Qt::AlignCenter, uavId);
    }

    // 显示当前无人机的详细状态
    if (dronesInfo.contains(currentSelectedUAV)) {
        const DroneInfo &info = dronesInfo[currentSelectedUAV];

        painter.setPen(Qt::white);
        painter.setFont(QFont("Arial", 10));

        int yPos = region.y() + 85;
        painter.drawText(region.x() + 10, yPos, QString("速度: %1 m/s").arg(QString::number(currentSpeed, 'f', 1)));

        yPos += 20;
        painter.drawText(region.x() + 10, yPos, QString("飞行时间: %1 s").arg(QString::number(flightTime, 'f', 1)));

        yPos += 20;
        painter.drawText(region.x() + 10, yPos, QString("飞行距离: %1 m").arg(QString::number(flightDistance, 'f', 1)));

        yPos += 20;
        painter.drawText(region.x() + 10, yPos, QString("状态: %1").arg(info.status));
    }
}

// 绘制指南针区域（右下）
void MainWindow::drawCompassRegion(QPainter &painter) {
    QRect region = regions.compassRegion;

    // 绘制区域边框
    painter.setPen(QPen(Qt::white, 2));
    painter.setBrush(QColor(50, 50, 50, 100));
    painter.drawRoundedRect(region, 10, 10);

    // 标题
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 12, QFont::Bold));
    painter.drawText(region.x() + 10, region.y() + 20, "飞行方向");

    // 绘制指南针
    QPoint compassCenter(region.center().x(), region.center().y() + 10);
    int compassRadius = qMin(region.width(), region.height()) / 3;

    // 指南针外圆
    painter.setPen(QPen(Qt::white, 2));
    painter.setBrush(QColor(0, 0, 0, 150));
    painter.drawEllipse(compassCenter, compassRadius, compassRadius);

    // 绘制方向刻度
    painter.setPen(QPen(Qt::lightGray, 1));
    painter.setFont(QFont("Arial", 8));

    // 主要方向标记
    QStringList directions = {"N", "E", "S", "W"};
    for (int i = 0; i < 4; ++i) {
        float angle = i * 90.0f * M_PI / 180.0f;
        float x1 = compassCenter.x() + (compassRadius - 10) * sin(angle);
        float y1 = compassCenter.y() - (compassRadius - 10) * cos(angle);
        float x2 = compassCenter.x() + compassRadius * sin(angle);
        float y2 = compassCenter.y() - compassRadius * cos(angle);

        painter.drawLine(QPointF(x1, y1), QPointF(x2, y2));

        // 方向字母
        float textX = compassCenter.x() + (compassRadius + 15) * sin(angle);
        float textY = compassCenter.y() - (compassRadius + 15) * cos(angle);
        painter.drawText(QPointF(textX - 5, textY + 5), directions[i]);
    }

    // 绘制当前飞行方向指针
    if (dronesInfo.contains(currentSelectedUAV)) {
        const DroneInfo &info = dronesInfo[currentSelectedUAV];

        // 计算飞行方向角度
        float vx = info.vx;
        float vy = info.vy;
        if (vx != 0 || vy != 0) {
            currentHeading = static_cast<int>(atan2(vx, -vy) * 180.0f / M_PI);
            if (currentHeading < 0) currentHeading += 360;

            // 计算当前速度
            currentSpeed = sqrt(vx * vx + vy * vy);
        }

        // 绘制方向指针
        painter.setPen(QPen(Qt::red, 3));
        float headingRad = currentHeading * M_PI / 180.0f;
        float pointerX = compassCenter.x() + (compassRadius - 20) * sin(headingRad);
        float pointerY = compassCenter.y() - (compassRadius - 20) * cos(headingRad);
        painter.drawLine(compassCenter, QPointF(pointerX, pointerY));

        // 指针箭头
        painter.setBrush(Qt::red);
        QPolygonF arrowHead;
        arrowHead << QPointF(pointerX, pointerY)
                  << QPointF(pointerX - 5 * sin(headingRad - 0.5), pointerY + 5 * cos(headingRad - 0.5))
                  << QPointF(pointerX - 5 * sin(headingRad + 0.5), pointerY + 5 * cos(headingRad + 0.5));
        painter.drawPolygon(arrowHead);
    }

    // 显示数字方向
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 10, QFont::Bold));
    painter.drawText(region.x() + 10, region.bottom() - 25, QString("方向: %1°").arg(currentHeading));
    painter.drawText(region.x() + 10, region.bottom() - 10, QString("速度: %1").arg(QString::number(currentSpeed, 'f', 1)));
}

// 更新雷达扫描
void MainWindow::updateRadarSweep() {
    radarSweepAngle += 6.0f; // 每次增加6度
    if (radarSweepAngle >= 360.0f) {
        radarSweepAngle = 0.0f;
    }

    // 只更新雷达区域
    update(regions.radarRegion);
}
