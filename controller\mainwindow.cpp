#include "mainwindow.h"
#include "ui_mainwindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    this->setWindowTitle("UAV控制终端");

    if (client.isInvalid()) {
        qDebug() << "Can not create VSOA client!";
        return; // 注意：这里不应该返回-1，因为构造函数没有返回值
    }

    // 正确连接信号和槽
    QObject::connect(&client, &QVsoaClient::connected,
                    this, &MainWindow::onConnected);
    QObject::connect(&client, &QVsoaClient::disconnected,
                    this, &MainWindow::onDisconnected);
    QObject::connect(&client, &QVsoaClient::datagram,
                    std::bind(&MainWindow::onDatagram, this, &client, _1, _2));

    // Connect to server with password
    client.connect2server("vsoa://127.0.0.1:3005/game_server", "", 1000);
    // Enable automatic connections
    client.autoConnect(1000, 500);
    // Subscribe /ctrl 订阅控制通道
    client.subscribe("/ctrl");
    // 订阅游戏数据通道
    client.subscribe("/game");
    // Enable data consistency on channels
    client.autoConsistent({"/ctrl", "/game"}, 1000);
    // 连接combobox选择变化信号
    connect(ui->id, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MainWindow::on_id_currentIndexChanged);

    // 设置UI控件
    ui->id->setFocusPolicy(Qt::NoFocus);

    // 初始化键盘计时器
    timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &MainWindow::joystickReturnedToCenter);
    timer->stop(); // 初始时计时器停止

    // 初始化控制命令发送计时器 (20Hz = 50ms)
    controlTimer = new QTimer(this);
    connect(controlTimer, &QTimer::timeout, this, &MainWindow::sendControlCommand);
    controlTimer->start(50); // 以20Hz的频率发送控制命令

    // 初始化摇杆参数
    radius = (height() - 220) / 6;
    gripWidth = (width() - 200) / 4;
    gripHeight = (height() - 220) / 3;
    gripY = height() - 200;
}

MainWindow::~MainWindow()
{
    // 停止计时器
    if (timer->isActive()) {
        timer->stop();
    }
    if (controlTimer->isActive()) {
        controlTimer->stop();
    }
    delete ui;
}

void MainWindow::paintEvent(QPaintEvent *event) {
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    // 设置画笔和画刷
    QPen pen(Qt::black, 2);
    painter.setPen(pen);
    painter.setBrush(Qt::white); // 设置填充颜色为白色

    // 1. 绘制遥控器主体
    QPainterPath bodyPath;
    bodyPath.addRoundedRect(100, 50, width() - 200, height() - 220, 100, 100);
    painter.drawPath(bodyPath);

    // 计算握柄尺寸和位置
    gripWidth = (width() - 200) / 4;
    gripHeight = (height() - 220) / 3;
    gripY = height() - 200;
    radius = (height() - 220) / 6;

    // 2. 绘制左下握柄（旋转 45°，显示在上层）
    {
        QPainterPath leftGrip;
        leftGrip.addRoundedRect(0, 0, gripWidth, gripHeight, 25, 25);

        // 创建变换：先移动到目标位置中心 -> 旋转 -> 移回
        QTransform transform;
        transform.translate(150 + gripWidth / 2, gripY + gripHeight / 2);
        transform.rotate(-60);
        transform.translate(-gripWidth / 2, -gripHeight / 2);

        QPainterPath rotatedLeftGrip = transform.map(leftGrip);
        painter.drawPath(rotatedLeftGrip); // 后绘制，覆盖到主体上
    }

    // 3. 绘制右下握柄（旋转 -45°，显示在上层）
    {
        QPainterPath rightGrip;
        rightGrip.addRoundedRect(0, 0, gripWidth, gripHeight, 25, 25);

        int rightX = width() - 150 - gripWidth;

        QTransform transform;
        transform.translate(rightX + gripWidth / 2, gripY + gripHeight / 2);
        transform.rotate(60);
        transform.translate(-gripWidth / 2, -gripHeight / 2);

        QPainterPath rotatedRightGrip = transform.map(rightGrip);
        painter.drawPath(rotatedRightGrip); // 后绘制，覆盖到主体上
    }

    // 4. 绘制摇杆
    // 设置摇杆背景
    QPointF joystickCenter(width()/ 2, gripY - radius - 30);
    painter.setPen(Qt::darkGray);
    painter.setBrush(QColor(240, 240, 240));  // 设置摇杆外圈背景
    painter.drawEllipse(joystickCenter, radius, radius);

    // 设置摇杆指针
    painter.setBrush(Qt::gray); // 设置指针颜色
    QPointF knobPos = joystickCenter + joystickPos;
    painter.drawEllipse(knobPos, 10, 10);  // 绘制摇杆指针
}

void MainWindow::mousePressEvent(QMouseEvent *event) {
    QPointF center(width()/ 2, gripY - radius - 30);
    if (QLineF(center, event->pos()).length() <= radius) {
        dragging = true;
        updateJoystickPosition(event->pos());
    }
}

// 在鼠标移动事件中，计算并更新角度，只在角度改变时发送信号
void MainWindow::mouseMoveEvent(QMouseEvent *event) {
    if (dragging) {
        updateJoystickPosition(event->pos());  // 更新摇杆位置

        int newAngle = getJoystickAngle();  // 获取新的角度

        // 如果角度发生变化
        if (newAngle != lastAngle) {
            lastAngle = newAngle;  // 更新最后的角度
        }
    }
}

void MainWindow::updateJoystickPosition(const QPointF &pos) {
    QPointF center(width()/ 2, gripY - radius - 30);
    joystickPos = pos - center;

    // 限制摇杆在圆内
    if (QLineF(QPointF(0, 0), joystickPos).length() > radius) {
        QLineF line(QPointF(0, 0), joystickPos);
        line.setLength(radius);
        joystickPos = line.p2();
    }
    
    // 只重绘摇杆区域
    QPointF joystickCenter(width()/ 2, gripY - radius - 30);
    QRect updateRect(joystickCenter.x() - radius - 15, 
                     joystickCenter.y() - radius - 15, 
                     (radius + 15) * 2, 
                     (radius + 15) * 2);
    update(updateRect);
}

void MainWindow::mouseReleaseEvent(QMouseEvent *event) {
    dragging = false;
    joystickPos = QPointF(0, 0);
    lastAngle = -1;
    qDebug() << "摇杆返回中心！";
    
    // 只重绘摇杆区域
    QPointF joystickCenter(width()/ 2, gripY - radius - 30);
    QRect updateRect(joystickCenter.x() - radius - 15, 
                     joystickCenter.y() - radius - 15, 
                     (radius + 15) * 2, 
                     (radius + 15) * 2);
    update(updateRect);
}

void MainWindow::joystickReturnedToCenter() {
    keyPressCount = 0;
    timer->stop();

    joystickPos = QPointF(0, 0);
    lastAngle = -1;
    
    // 只重绘摇杆区域
    QPointF joystickCenter(width()/ 2, gripY - radius - 30);
    QRect updateRect(joystickCenter.x() - radius - 15, 
                     joystickCenter.y() - radius - 15, 
                     (radius + 15) * 2, 
                     (radius + 15) * 2);
    update(updateRect);
}

void MainWindow::keyPressEvent(QKeyEvent *event) {
    constexpr float step = 5.0f;
    switch (event->key()) {
       case Qt::Key_Up:
           joystickPos.setY(joystickPos.y() - step);
           break;
       case Qt::Key_Down:
           joystickPos.setY(joystickPos.y() + step);
           break;
       case Qt::Key_Left:
           joystickPos.setX(joystickPos.x() - step);
           break;
       case Qt::Key_Right:
           joystickPos.setX(joystickPos.x() + step);
           break;
       default:
           return;
    }

    // 保证摇杆指针始终保持在圆内
    if (QLineF(0, 0, joystickPos.x(), joystickPos.y()).length() > radius) {
        QLineF line(QPointF(0, 0), joystickPos);
        line.setLength(radius);
        joystickPos = line.p2();
    }

    int newAngle = getJoystickAngle();

    if(joystickPos == QPointF(0, 0)) {
        lastAngle = -1;
    } else if (newAngle != lastAngle) {
        qDebug() << "角度变化：" << lastAngle << "->" << newAngle;
        lastAngle = newAngle;
    }

    // 只重绘摇杆区域
    QPointF joystickCenter(width()/ 2, gripY - radius - 30);
    QRect updateRect(joystickCenter.x() - radius - 15, 
                     joystickCenter.y() - radius - 15, 
                     (radius + 15) * 2, 
                     (radius + 15) * 2);
    update(updateRect);
    
    // 开始计时，或重置计时器
    if (keyPressCount == 0) {
        timer->start(1000); // 启动计时器
    } else {
        timer->stop();
        timer->start(1000); // 重启计时器
    }

    keyPressCount++;
}

int MainWindow::getJoystickAngle() const {                // 计算摇杆指针的角度（返回值为角度）
    if (joystickPos.isNull()) {
        return -1;  // 中心位置
    }

    // 计算角度（0度为向右，90度为向下）
    double angle = qAtan2(joystickPos.y(), joystickPos.x()) * 180 / M_PI;
    return static_cast<int>(angle);
}

// 处理combobox选择变化
void MainWindow::on_id_currentIndexChanged(int index)
{
    // 根据选择的索引设置当前无人机ID
    // B1对应ID=1，B2对应ID=2，B3对应ID=3
    currentDroneId = index + 1;

//    qDebug() << "当前选择的无人机ID：" << currentDroneId;

    // 更新UI显示
    updateUIDisplay();
}

void MainWindow::onConnected(bool ok, QString info)
{
    if (!ok) {
        qDebug() << "Connected with server failed!";
        return;
    }
    qDebug() << "Connected with server:" << info;
}

void MainWindow::onDisconnected()
{
    qDebug() << "Connection break";
}

void MainWindow::onDatagram(QVsoaClient *client, QString url, QVsoaPayload payload)
{
//    qDebug() << "Datagram received from URL" << url << "with payload:" << payload.param();

    QVariant param = payload.param();
    // 处理接收到的数据
    if (url == "/game") {
        // 处理游戏数据
        QString dataStr;

        // 根据实际类型转换
        if (param.type() == QVariant::String) {
            dataStr = param.toString();
        } else if (param.canConvert<QByteArray>()) {
            dataStr = QString::fromUtf8(param.toByteArray());
        } else {
            qDebug() << "不支持的参数类型:" << param.typeName();
            return;
        }

        processGameData(dataStr);
    } else if (url == "/ctrl") {
        // 处理控制数据的反馈
        qDebug() << "接收到控制数据反馈：" << param;
    }
}


void MainWindow::processGameData(const QString &jsonData)         // 处理服务器发来的游戏数据
{
    QJsonDocument doc = QJsonDocument::fromJson(jsonData.toUtf8());
    if (doc.isNull() || !doc.isObject()) {
        qDebug() << "无效的JSON数据";
        return;
    }

    QJsonObject gameObj = doc.object();

    // 更新游戏状态
    if (gameObj.contains("left_time")) {
        gameLeftTime = gameObj["left_time"].toInt();
    }

    if (gameObj.contains("stage")) {
        gameStage = gameObj["stage"].toString();
    }

    // 更新无人机信息
    if (gameObj.contains("drones") && gameObj["drones"].isArray()) {
        QJsonArray drones = gameObj["drones"].toArray();

        // 保存当前选择的无人机ID
        QString currentUid = ui->id->currentText();

        // 记录当前选择的无人机信息（如果存在）
        DroneInfo oldInfo;
        bool hadInfo = dronesInfo.contains(currentUid);
        if (hadInfo) {
            oldInfo = dronesInfo[currentUid];
        }

        // 清空之前的数据
        dronesInfo.clear();

        // 更新无人机信息
        for (const QJsonValue &droneValue : drones) {
            if (droneValue.isObject()) {
                QJsonObject drone = droneValue.toObject();

                DroneInfo info;
                info.hp = drone["hp"].toInt();
                info.status = drone["status"].toString();
                info.team = drone["team"].toString();
                info.uid = drone["uid"].toString();
                info.x = drone["x"].toDouble();
                info.y = drone["y"].toDouble();

                // vx和vy可能不存在于所有无人机数据中
                if (drone.contains("vx")) {
                    info.vx = drone["vx"].toDouble();
                }

                if (drone.contains("vy")) {
                    info.vy = drone["vy"].toDouble();
                }

                // 存储无人机信息
                dronesInfo[info.uid] = info;
            }
        }
        // 只有当前选择的无人机信息发生变化时才更新UI
        bool needUpdate = !hadInfo || !dronesInfo.contains(currentUid) ||
                         (dronesInfo[currentUid].hp != oldInfo.hp) ||
                         (dronesInfo[currentUid].x != oldInfo.x) ||
                         (dronesInfo[currentUid].y != oldInfo.y) ||
                         (dronesInfo[currentUid].status != oldInfo.status);

        if (needUpdate) {
            updateUIDisplay();
        }
    }
    // 更新UI显示
    updateUIDisplay();
}


void MainWindow::updateUIDisplay()          // 更新遥控器UI显示
{
    // 获取当前选择的无人机ID
    QString currentUid = ui->id->currentText();

    // 如果存在该无人机的信息，则更新UI
    if (dronesInfo.contains(currentUid)) {
        DroneInfo &info = dronesInfo[currentUid];

        // 更新血量
        ui->hp->setValue(info.hp);

        // 更新坐标
        ui->point->setText(QString("(%1, %2)").arg(info.x, 0, 'f', 1).arg(info.y, 0, 'f', 1));

        // 如果无人机已坠毁，显示提示
        if (info.status == "down") {
            ui->hp->setValue(0);
            // 可以添加其他提示，如改变颜色等
        }
    } else {
        // 如果没有该无人机的信息，清空显示
        ui->hp->setValue(0);

        // 对于敌方无人机，显示特殊信息
        if (currentUid.startsWith("R")) {
            ui->point->setText("未知 ");
        } else {
            ui->point->setText("(0, 0)");
        }
    }

    // 设置状态栏文本颜色和字体大小
    statusBar()->showMessage(QString("游戏状态: %1, 剩余时间: %2秒").arg(gameStage).arg(gameLeftTime));
}


void MainWindow::sendControlCommand()            // 发送控制命令到服务器
{
    // 获取当前角度
    int angle = getJoystickAngle();

    // 如果摇杆在中心位置，发送速度为0的命令
    double vx = 0.0;
    double vy = 0.0;

    if (angle != -1) {
            // 将角度转换为弧度
            double radians = angle * M_PI / 180.0;

            // 计算摇杆距离中心的比例 (0.0 到 1.0)
            double distanceRatio = QLineF(QPointF(0, 0), joystickPos).length() / radius;

            // 计算初始vx和vy
            double rawVx = qCos(radians);
            double rawVy = qSin(radians);

            // 计算缩放因子，确保vx和vy的最大值为50
            double maxComponent = qMax(qAbs(rawVx), qAbs(rawVy));
            double scaleFactor = 50.0 / maxComponent;

            // 应用缩放因子和距离比例
            vx = rawVx * scaleFactor * distanceRatio;
            vy = rawVy * scaleFactor * distanceRatio;
        }

    // 获取当前选择的无人机ID文本
    QString droneId = ui->id->currentText();

    // 创建JSON对象
    QJsonObject controlObj;
    controlObj["uid"] = droneId;  // 直接使用下拉框中的文本
    controlObj["vx"] = vx;
    controlObj["vy"] = vy;

    // 转换为JSON文档
    QJsonDocument doc(controlObj);
    QString jsonString = doc.toJson(QJsonDocument::Compact);

    // 创建payload并发送
    QVsoaPayload payload;
    payload.setParam(jsonString);

    // 发送到/ctrl通道
    client.sendDatagram("/ctrl", payload);

//    // 调试输出
//    if (angle != -1) {
//        qDebug() << "发送控制命令: uid=" << droneId
//                 << ", vx=" << vx << ", vy=" << vy
//                 << ", 角度=" << angle;
//    }
}
