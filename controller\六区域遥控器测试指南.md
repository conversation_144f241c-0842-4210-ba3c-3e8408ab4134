# 六区域遥控器界面测试指南

## 🎮 测试环境准备

### 前置条件
1. **Qt开发环境**：确保安装了Qt 5.x或6.x版本
2. **QVSOA库**：项目依赖的通信库
3. **游戏服务器**：需要运行UAV对战游戏服务器
4. **编译环境**：支持C++11的编译器

### 编译步骤
```bash
cd controller
qmake controller.pro
make
```

## 🔍 功能测试清单

### 1. 界面布局测试
- [ ] **窗口启动**：程序正常启动，显示六个区域
- [ ] **区域划分**：界面正确分为3x2网格布局
- [ ] **边框颜色**：每个区域有不同颜色的边框
- [ ] **响应式布局**：调整窗口大小时区域自动调整

### 2. 左上区域 - 我方信息测试
- [ ] **无人机列表**：显示B1、B2、B3三架无人机
- [ ] **血量显示**：正确显示每架无人机的HP值
- [ ] **血量条**：血量条颜色根据HP值变化（绿/黄/红）
- [ ] **位置坐标**：实时显示无人机的(x,y)坐标
- [ ] **选择高亮**：当前选择的无人机有黄色高亮背景
- [ ] **状态更新**：无人机信息实时更新

### 3. 中上区域 - 模拟雷达测试
- [ ] **雷达圆形**：显示圆形雷达屏幕
- [ ] **扫描动画**：雷达扫描线以6度/100ms速度旋转
- [ ] **网格线**：显示十字网格线
- [ ] **敌方目标**：红色圆点显示敌方无人机位置
- [ ] **障碍物显示**：
  - [ ] 山体障碍物（绿色圆圈，标记"M"）
  - [ ] 雷达障碍物（紫色圆圈，标记"R"）
  - [ ] 雷云障碍物（黄色圆圈，标记"C"）
- [ ] **相对位置**：以当前选择无人机为中心
- [ ] **距离缩放**：300像素范围内的目标按比例显示
- [ ] **探测范围指示**：青色虚线圆圈显示300像素探测边界
- [ ] **目标标识**：显示目标ID和障碍物ID

### 4. 右上区域 - 敌方信息测试
- [ ] **敌方列表**：显示R1、R2、R3三架敌方无人机
- [ ] **血量显示**：显示敌方无人机HP值
- [ ] **血量条**：敌方血量条使用红色系
- [ ] **位置坐标**：显示敌方无人机位置
- [ ] **距离计算**：显示与当前选择无人机的距离
- [ ] **威胁提示**：显示最近的威胁障碍物信息
- [ ] **威胁距离**：显示到威胁障碍物的距离

### 5. 左下区域 - 摇杆控制测试
- [ ] **摇杆背景**：显示圆形摇杆背景
- [ ] **方向线**：显示十字方向指示线
- [ ] **鼠标控制**：
  - [ ] 鼠标拖拽控制摇杆指针
  - [ ] 摇杆指针限制在圆形范围内
  - [ ] 松开鼠标摇杆自动回中
- [ ] **键盘控制**：
  - [ ] 方向键控制摇杆位置
  - [ ] 摇杆位置实时更新
- [ ] **状态显示**：
  - [ ] 显示当前角度（0-359度）
  - [ ] 显示控制强度（0-100%）
  - [ ] 中心位置显示"中心位置"

### 6. 中下区域 - 状态信息测试
- [ ] **无人机选择**：
  - [ ] 显示B1、B2、B3选择按钮
  - [ ] 当前选择的按钮有黄色高亮
  - [ ] 点击按钮切换控制无人机
- [ ] **状态信息**：
  - [ ] 显示当前控制的无人机ID
  - [ ] 显示当前速度（m/s）
  - [ ] 显示飞行时间（秒）
  - [ ] 显示飞行距离（米）
  - [ ] 显示无人机状态

### 7. 右下区域 - 指南针测试
- [ ] **指南针圆形**：显示圆形指南针
- [ ] **方向标记**：显示N、E、S、W四个主要方向
- [ ] **方向指针**：
  - [ ] 红色箭头指向当前飞行方向
  - [ ] 箭头根据速度向量实时计算
  - [ ] 箭头有三角形箭头头部
- [ ] **数字显示**：
  - [ ] 显示数字方向角度（0-359度）
  - [ ] 显示当前速度值
- [ ] **实时更新**：方向和速度实时更新

## 🔄 数据通信测试

### QVSOA通信测试
- [ ] **连接建立**：程序启动时成功连接到游戏服务器
- [ ] **数据接收**：正确接收/game频道的游戏数据
- [ ] **数据解析**：
  - [ ] 正确解析无人机数据（drones数组）
  - [ ] 正确解析障碍物数据（obstacles数组）
  - [ ] 正确解析游戏状态（stage、left_time）
- [ ] **控制发送**：摇杆控制正确发送到/ctrl频道
- [ ] **JSON格式**：控制命令使用正确的JSON格式

### 数据更新测试
- [ ] **实时性**：界面数据50ms周期更新
- [ ] **一致性**：所有区域显示的数据保持一致
- [ ] **准确性**：显示的数据与服务器数据匹配

## 🎯 交互测试

### 无人机切换测试
1. **测试步骤**：
   - 点击状态区域的B1按钮
   - 观察各区域变化
   - 点击B2按钮
   - 观察各区域变化
   - 点击B3按钮
   - 观察各区域变化

2. **预期结果**：
   - 我方信息区域高亮显示选择的无人机
   - 雷达区域以选择的无人机为中心
   - 指南针显示选择无人机的方向
   - 状态信息更新为选择的无人机

### 摇杆控制测试
1. **鼠标控制测试**：
   - 在摇杆区域按下鼠标
   - 拖拽到不同位置
   - 观察摇杆指针移动
   - 观察角度和强度显示
   - 松开鼠标观察回中

2. **键盘控制测试**：
   - 按下方向键
   - 观察摇杆指针移动
   - 观察控制命令发送

## 🐛 常见问题排查

### 编译问题
- **qmake未找到**：检查Qt安装和PATH环境变量
- **QVSOA库缺失**：确保项目包含QVSOA库文件
- **编译错误**：检查C++11支持和Qt版本兼容性

### 运行问题
- **界面显示异常**：检查窗口大小和分辨率
- **数据不更新**：检查网络连接和服务器状态
- **摇杆不响应**：检查鼠标事件处理和区域计算

### 性能问题
- **界面卡顿**：检查绘制频率和数据更新频率
- **内存泄漏**：检查定时器和对象生命周期
- **CPU占用高**：优化绘制算法和数据处理

## 📊 性能指标

### 目标性能
- **界面刷新率**：≥20 FPS
- **数据延迟**：≤100ms
- **内存占用**：≤100MB
- **CPU占用**：≤10%

### 测试方法
- 使用Qt Creator的性能分析工具
- 监控系统资源使用情况
- 测试长时间运行稳定性

## 🎉 验收标准

### 基本功能
- [ ] 所有六个区域正常显示
- [ ] 数据实时更新无延迟
- [ ] 摇杆控制响应正常
- [ ] 无人机切换功能正常

### 高级功能
- [ ] 障碍物正确显示在雷达中
- [ ] 威胁提示功能正常
- [ ] 指南针方向计算准确
- [ ] 界面美观用户体验良好

### 稳定性
- [ ] 长时间运行无崩溃
- [ ] 网络断线重连正常
- [ ] 异常数据处理正常
- [ ] 内存使用稳定

通过以上测试，确保六区域遥控器界面功能完整、性能稳定、用户体验良好。
