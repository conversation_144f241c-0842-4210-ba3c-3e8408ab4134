# Strategy2 强化学习设计文档

## 1. 概述

Strategy2是一个基于MADDPG（Multi-Agent Deep Deterministic Policy Gradient）算法的多智能体强化学习系统，专门用于无人机集群的协同控制。该系统采用Actor-Critic架构，通过离线训练和在线推理的方式实现智能体的自主决策。

## 2. 系统架构

### 2.1 整体架构

#### 2.1.1 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Python训练环境 │    ┌─────────────────┐    │   Qt仿真环境     │
│                 │    │   C++推理引擎   │    │                 │
│  MADDPG算法     │───▶│  Strategy2类    │───▶│  无人机控制     │
│  PyTorch模型    │    │  神经网络推理   │    │  实时仿真       │
│  JSON参数导出   │    │  避障算法       │    │  可视化界面     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

#### 2.1.2 详细阶段流程

##### 阶段1：训练阶段（Python环境）
```
┌─────────────────────────────────────────────────────────────┐
│                    Python训练环境                          │
├─────────────────────────────────────────────────────────────┤
│ 1. 环境初始化                                             │
│    ├── 创建多智能体环境                                   │
│    ├── 初始化MADDPG算法                                  │
│    └── 设置网络架构（Actor-Critic）                       │
│                                                           │
│ 2. 训练循环                                              │
│    ├── 收集观察向量 (31维)                               │
│    ├── Actor网络输出动作 (2维)                           │
│    ├── 环境执行动作，获得奖励                             │
│    ├── 存储经验到回放缓冲区                               │
│    ├── 更新Critic网络（集中式训练）                       │
│    └── 更新Actor网络（分散式执行）                        │
│                                                           │
│ 3. 模型保存                                             │
│    ├── 保存Actor网络参数                                 │
│    ├── 保存Critic网络参数                                │
│    └── 导出为JSON格式                                    │
└─────────────────────────────────────────────────────────────┘
```

**关键特点：**
- **集中式训练**：Critic网络可以访问所有智能体的状态和动作信息
- **分散式执行**：每个智能体只使用自己的Actor网络进行决策
- **经验回放**：存储历史交互数据，提高训练稳定性
- **目标网络**：使用目标网络减少训练过程中的相关性

##### 阶段2：模型导出阶段（Python → C++）
```
┌─────────────────────────────────────────────────────────────┐
│                   模型参数导出                             │
├─────────────────────────────────────────────────────────────┤
│ 1. 网络参数提取                                          │
│    ├── 提取Actor网络权重矩阵                             │
│    ├── 提取偏置向量                                      │
│    └── 记录网络架构信息                                  │
│                                                           │
│ 2. 参数转换                                              │
│    ├── 将PyTorch张量转换为C++数组                        │
│    ├── 保持数值精度                                      │
│    └── 验证参数完整性                                    │
│                                                           │
│ 3. JSON格式导出                                          │
│    ├── 结构化存储网络参数                                │
│    ├── 包含网络维度信息                                  │
│    └── 便于C++解析和加载                                 │
└─────────────────────────────────────────────────────────────┘
```

**导出格式示例：**
```json
{
  "fc1.weight": [[w11, w12, ...], [w21, w22, ...], ...],
  "fc1.bias": [b1, b2, ...],
  "fc2.weight": [[w11, w12, ...], [w21, w22, ...], ...],
  "fc2.bias": [b1, b2, ...],
  "pi.weight": [[w11, w12, ...], [w21, w22, ...]],
  "pi.bias": [b1, b2]
}
```

##### 阶段3：推理阶段（C++引擎）
```
┌─────────────────────────────────────────────────────────────┐
│                   C++推理引擎 (Strategy2)                  │
├─────────────────────────────────────────────────────────────┤
│ 1. 模型加载                                              │
│    ├── 读取JSON文件                                      │
│    ├── 解析网络参数                                      │
│    ├── 初始化网络权重和偏置                              │
│    └── 设置网络维度                                      │
│                                                           │
│ 2. 推理执行                                              │
│    ├── 输入验证 (31维观察向量)                           │
│    ├── 前向传播计算                                      │
│    │   ├── fc1_output = LeakyReLU(fc1(observation))     │
│    │   ├── fc2_output = LeakyReLU(fc2(fc1_output))     │
│    │   └── pi_output = Softsign(pi(fc2_output))         │
│    ├── 输出处理 (2维动作向量)                            │
│    └── 返回归一化动作                                    │
│                                                           │
│ 3. 混合策略决策                                          │
│    ├── 检查特殊标志位 (observation[30])                  │
│    ├── 神经网络推理策略                                  │
│    └── 自定义避障追踪策略                                │
└─────────────────────────────────────────────────────────────┘
```

**推理流程详解：**
1. **输入预处理**：验证观察向量维度，反归一化关键数据
2. **策略选择**：根据标志位选择学习策略或规则策略
3. **神经网络推理**：执行三层全连接网络的前向传播
4. **动作输出**：返回[-1,1]范围内的归一化动作

##### 阶段4：部署阶段（Qt仿真环境）
```
┌─────────────────────────────────────────────────────────────┐
│                   Qt仿真环境                              │
├─────────────────────────────────────────────────────────────┤
│ 1. 环境初始化                                            │
│    ├── 创建无人机实例                                    │
│    ├── 初始化Strategy2推理引擎                           │
│    ├── 加载训练好的模型参数                              │
│    └── 设置仿真参数（时间步长、最大速度等）              │
│                                                           │
│ 2. 仿真循环                                              │
│    ├── 收集环境状态                                      │
│    │   ├── 无人机位置和速度                              │
│    │   ├── 目标位置                                      │
│    │   ├── 障碍物信息                                    │
│    │   └── 其他智能体状态                                │
│    ├── 构建观察向量 (31维)                               │
│    ├── 调用Strategy2推理                                 │
│    ├── 执行动作控制                                      │
│    └── 更新环境状态                                      │
│                                                           │
│ 3. 实时控制                                              │
│    ├── 速度控制 (getVelocity)                            │
│    ├── 目标速度计算 (getTargetVelocity)                  │
│    ├── 物理约束处理                                      │
│    └── 碰撞检测和响应                                    │
└─────────────────────────────────────────────────────────────┘
```

**部署特点：**
- **实时性**：单次推理时间 < 1ms
- **稳定性**：支持长时间运行
- **可视化**：提供实时仿真界面
- **多智能体**：支持多个无人机协同控制

#### 2.1.3 数据流分析

**训练阶段数据流：**
```
观察向量 → Actor网络 → 动作 → 环境 → 奖励 → Critic网络 → 策略更新
```

**推理阶段数据流：**
```
环境状态 → 观察向量 → Strategy2推理 → 动作输出 → 速度控制 → 无人机运动
```

**关键数据转换：**
1. **观察向量归一化**：物理坐标转换为[0,1]或[-1,1]范围
2. **动作反归一化**：网络输出的[-1,1]动作转换为实际控制指令
3. **速度映射**：加速度分量转换为实际速度向量

#### 2.1.4 系统优势

1. **模块化设计**：训练、推理、部署各阶段独立，便于维护和升级
2. **跨平台兼容**：Python训练 + C++推理 + Qt部署的灵活组合
3. **实时性能**：C++推理引擎保证实时性要求
4. **混合策略**：结合学习能力和规则控制，提高系统鲁棒性
5. **可扩展性**：支持不同网络架构和决策策略的灵活配置

### 2.2 核心组件

Strategy2系统的核心组件基于MADDPG算法设计，包含5个主要组件，形成完整的多智能体强化学习系统：

#### 2.2.1 Actor网络

Actor网络是MADDPG算法的核心策略网络，负责学习智能体的策略函数π(s)。它将观察向量映射为连续动作，采用确定性策略直接输出动作，无需采样。每个智能体拥有独立的Actor网络，实现分散式执行。

| 属性 | 描述 |
|------|------|
| **功能** | 策略网络，学习状态到动作的映射 |
| **输入** | 31维观察向量 |
| **输出** | 2维动作向量 [-1,1] |
| **策略类型** | 确定性策略 |
| **执行方式** | 分散式执行 |
| **训练目标** | 最大化期望累积奖励 |
| **核心函数** | `policy_network(observation) → action` |

#### 2.2.2 Critic网络

Critic网络是价值网络，用于评估状态-动作对的价值Q(s,a)。在训练阶段，它可以访问所有智能体的状态和动作信息，采用集中式训练方式。Critic网络为Actor网络提供策略梯度，指导策略的优化方向。

| 属性 | 描述 |
|------|------|
| **功能** | 价值网络，评估状态-动作对的价值 |
| **输入** | 所有智能体的状态和动作 |
| **输出** | Q值 |
| **训练方式** | 集中式训练 |
| **作用** | 为Actor网络提供策略梯度 |
| **训练目标** | 最小化价值估计误差 |
| **核心函数** | `value_network(states, actions) → q_value` |

#### 2.2.3 经验回放缓冲区

经验回放缓冲区存储智能体与环境交互的历史经验，通过随机采样打破数据的时间相关性，提高训练稳定性。它使用循环缓冲区结构，支持批量处理，是深度强化学习训练的关键组件。

| 属性 | 描述 |
|------|------|
| **功能** | 存储历史交互数据，打破相关性 |
| **存储内容** | (state, action, reward, next_state, done) |
| **缓冲区类型** | 循环缓冲区 |
| **采样方式** | 随机采样 |
| **处理方式** | 批量处理 |
| **容量管理** | 固定大小 |
| **核心函数** | `store_experience()`<br>`sample_batch() → batch` |

#### 2.2.4 目标网络

目标网络通过软更新和硬更新机制稳定训练过程，减少训练过程中的相关性。它包含目标Actor网络和目标Critic网络，通过缓慢更新目标网络参数，避免训练过程中的震荡，提高收敛稳定性。

| 属性 | 描述 |
|------|------|
| **功能** | 稳定训练过程，减少相关性 |
| **网络类型** | 目标Actor网络、目标Critic网络 |
| **更新机制** | 软更新、硬更新 |
| **软更新公式** | `target = τ × source + (1-τ) × target` |
| **硬更新方式** | 完全复制主网络参数 |
| **更新频率** | 软更新：每次训练<br>硬更新：定期执行 |
| **核心函数** | `soft_update(source, target, τ)`<br>`hard_update(source, target)` |

#### 2.2.5 混合策略控制器

混合策略控制器是Strategy2系统的特色组件，结合了学习策略和规则策略的优势。它根据观察向量的标志位选择不同的决策策略，在保证系统安全性的同时，充分利用神经网络的智能决策能力。

| 属性 | 描述 |
|------|------|
| **功能** | 结合学习策略和规则策略 |
| **策略类型** | 学习策略、规则策略 |
| **选择机制** | 根据标志位选择 |
| **规则策略** | 基于几何和物理的避障追踪 |
| **学习策略** | 基于神经网络的智能决策 |
| **安全保证** | 确保系统在异常情况下的安全性 |
| **核心函数** | `select_strategy(observation)`<br>`rule_based_decision()`<br>`neural_network_decision()` |

## 3. 强化学习算法设计

### 3.1 MADDPG算法原理

MADDPG是多智能体版本的DDPG算法，具有以下特点：

1. **集中式训练，分散式执行**: 训练时Critic可以访问所有智能体的信息，执行时每个智能体只使用自己的Actor
2. **确定性策略**: 直接输出连续动作，无需采样
3. **多智能体协调**: 考虑智能体间的相互影响

### 3.2 网络架构设计

#### Actor网络结构
```
输入层 (31维) → 全连接层1 → LeakyReLU → 全连接层2 → LeakyReLU → 输出层 → Softsign
```

- **输入维度**: 31维观察向量
- **隐藏层1**: 可配置维度，使用LeakyReLU激活
- **隐藏层2**: 可配置维度，使用LeakyReLU激活  
- **输出层**: 2维动作向量，使用Softsign激活（输出范围[-1,1]）

#### 激活函数选择
- **LeakyReLU**: 解决梯度消失问题，允许负值梯度传播
- **Softsign**: 输出平滑的[-1,1]范围，适合连续控制

### 3.3 状态空间设计

观察向量包含31个维度：
```
[0-1]   自身位置 (x, y) - 归一化到[0,1]
[2-3]   自身速度 (vx, vy) - 归一化到[-1,1]  
[4-19]  其他智能体信息 (位置、速度等)
[20-21] 目标位置 (x, y) - 归一化到[0,1]
[22-29] 障碍物信息 (位置、速度、半径)
[30]    特殊标志位 (用于触发自定义避障逻辑)
```

### 3.4 动作空间设计

- **维度**: 2维连续动作空间
- **范围**: [-1, 1] × [-1, 1]
- **含义**: 加速度方向分量 (ax, ay)
- **映射**: 通过Softsign函数确保输出范围

## 4. 智能体策略设计

### 4.1 混合策略架构

Strategy2采用混合策略设计，结合了学习策略和规则策略：

```cpp
if (observation[30] > 0.5f) {
    return customAvoidanceAndTracking(observation);  // 规则策略
} else {
    return neuralNetworkInference(observation);      // 学习策略
}
```

### 4.2 学习策略（神经网络推理）

1. **前向传播**: 执行三层全连接网络的前向传播
2. **激活函数**: 使用LeakyReLU和Softsign
3. **输出处理**: 直接输出归一化动作

### 4.3 规则策略（自定义避障追踪）

#### 4.3.1 安全角度计算
```cpp
// 计算障碍物的不安全角度范围
for (每个障碍物) {
    if (障碍物距离 < 250) {
        计算障碍物角度范围;
        更新安全角度范围;
    }
}
```

#### 4.3.2 决策逻辑
1. **有目标且目标安全**: 直接飞向目标
2. **有目标但目标不安全**: 选择最接近目标的安全角度
3. **无目标且当前方向安全**: 保持当前方向
4. **无目标且当前方向不安全**: 选择最接近当前方向的安全角度
5. **无目标无当前方向**: 使用预设搜索角度

## 5. 训练环境设计

### 5.1 奖励函数设计

建议的奖励函数结构：
```python
def calculate_reward(self, agent_id, observation, action, next_observation):
    reward = 0
    
    # 目标追踪奖励
    if has_target:
        distance_to_target = calculate_distance(agent_pos, target_pos)
        reward += -distance_to_target * 0.1
    
    # 避障奖励
    for obstacle in obstacles:
        distance = calculate_distance(agent_pos, obstacle_pos)
        if distance < safe_distance:
            reward += -100  # 碰撞惩罚
    
    # 速度奖励
    speed = calculate_speed(agent_velocity)
    reward += speed * 0.01  # 鼓励移动
    
    # 动作平滑奖励
    action_magnitude = np.linalg.norm(action)
    reward += -action_magnitude * 0.01  # 鼓励平滑动作
    
    return reward
```

### 5.2 环境交互设计

1. **状态转换**: 基于物理模型更新智能体状态
2. **碰撞检测**: 实时检测智能体与障碍物的碰撞
3. **边界处理**: 处理智能体超出边界的情况
4. **多智能体协调**: 考虑智能体间的相互影响

## 6. 实现细节

### 6.1 模型加载机制

```cpp
bool Strategy2::loadModel(const QString &filePath) {
    // 1. 读取JSON文件
    // 2. 解析网络参数
    // 3. 初始化网络权重和偏置
    // 4. 设置网络维度
}
```

### 6.2 推理过程

```cpp
QPointF Strategy2::inference(const QVector<float> &observation) {
    // 1. 输入验证
    // 2. 第一层前向传播: fc1_output = LeakyReLU(fc1(observation))
    // 3. 第二层前向传播: fc2_output = LeakyReLU(fc2(fc1_output))
    // 4. 输出层前向传播: pi_output = Softsign(pi(fc2_output))
    // 5. 返回归一化动作
}
```

### 6.3 速度控制

```cpp
QPointF Strategy2::getVelocity(const QVector<float> &observation) {
    // 1. 获取模型输出的加速度分量
    // 2. 更新当前速度: v_new = v_current + a * dt
    // 3. 限制速度大小不超过vMax
    // 4. 返回更新后的速度
}
```

## 7. 性能优化

### 7.1 计算优化
- **内存预分配**: 网络权重和中间结果预分配内存
- **向量化计算**: 使用SIMD指令加速矩阵运算
- **缓存友好**: 优化数据访问模式

### 7.2 实时性保证
- **推理时间**: 单次推理 < 1ms
- **内存使用**: 模型参数 < 1MB
- **线程安全**: 支持多线程并发推理

## 8. 扩展性设计

### 8.1 模块化架构
- **网络模块**: 可插拔的网络架构
- **策略模块**: 支持多种决策策略
- **环境模块**: 支持不同仿真环境

### 8.2 配置化设计
- **网络参数**: 通过JSON文件配置
- **超参数**: 支持运行时调整
- **策略参数**: 可配置的决策逻辑

## 9. 测试与验证

### 9.1 单元测试
- **网络推理**: 验证前向传播的正确性
- **模型加载**: 测试不同格式的模型文件
- **边界条件**: 测试异常输入的处理

### 9.2 集成测试
- **端到端测试**: 完整的训练-推理流程
- **性能测试**: 验证实时性要求
- **稳定性测试**: 长时间运行测试

## 10. 未来改进方向

### 10.1 算法改进
- **PPO算法**: 更稳定的策略梯度算法
- **SAC算法**: 基于最大熵的强化学习
- **分层强化学习**: 分解复杂任务

### 10.2 功能扩展
- **在线学习**: 支持在线策略更新
- **多任务学习**: 支持多种任务类型
- **迁移学习**: 支持跨环境的知识迁移

### 10.3 工程优化
- **GPU加速**: 支持GPU推理
- **分布式训练**: 支持多机训练
- **模型压缩**: 减少模型大小和推理时间

## 11. 总结

Strategy2是一个设计良好的多智能体强化学习系统，通过混合策略设计实现了学习能力和规则控制的结合。该系统具有良好的扩展性和实时性，为无人机集群的智能控制提供了有效的解决方案。

关键特点：
1. **混合策略**: 结合神经网络学习和规则控制
2. **实时推理**: 满足实时控制的时间要求
3. **模块化设计**: 便于维护和扩展
4. **鲁棒性**: 具备完善的异常处理机制 