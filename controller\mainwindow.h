#ifndef MAINWINDOW_H
#define MAINWINDOW_H
#include <QMainWindow>
#include <QPainter>
#include <QPainterPath>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QPointF>
#include <QtMath>
#include <qdebug>
#include <QComboBox>
#include <QTimer>
#include <QLabel>
#include <QVsoa>
#include <functional>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QMap>
#include <QString>

using namespace std::placeholders;

// 定义无人机信息结构体
struct DroneInfo {
    int hp;
    QString status;
    QString team;
    QString uid;
    double vx;
    double vy;
    double x;
    double y;
};

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;

private slots:
    // 处理combobox选择变化的槽函数
    void on_id_currentIndexChanged(int index);

    // 发送控制命令到服务器
    void sendControlCommand();

    // 更新UI显示
    void updateUIDisplay();

private:
    Ui::MainWindow *ui;
    QVsoaClient client;
    QPointF joystickPos;             // 摇杆位置（相对位置）
    float radius;                    // 摇杆的半径
    int gripWidth, gripHeight, gripY;
    bool dragging = false;
    int lastAngle = -1;              // 用于记录上一个角度，-1表示摇杆位于圆心无人机原地不动

    void updateJoystickPosition(const QPointF &pos);
    int getJoystickAngle() const;
    void joystickReturnedToCenter();
    QTimer *timer;                   // 键盘操作计时器
    QTimer *controlTimer;            // 控制命令发送计时器
    int keyPressCount = 0;           // 按键按下次数
    // 添加当前选择的无人机ID
    int currentDroneId = 1;

    // 添加QVSOA通信相关函数
    void onConnected(bool ok, QString info);
    void onDisconnected();
    void onDatagram(QVsoaClient *client, QString url, QVsoaPayload payload);

    // 添加处理游戏数据的函数
    void processGameData(const QString &jsonData);

    // 存储无人机信息的Map
    QMap<QString, DroneInfo> dronesInfo;

    // 游戏状态信息
    int gameLeftTime = 0;
    QString gameStage = "init";

    // 六区域遥控器相关变量
    struct ControllerRegions {
        QRect friendlyInfoRegion;    // 左上：我方信息
        QRect radarRegion;           // 中上：模拟雷达
        QRect enemyInfoRegion;       // 右上：敌方信息
        QRect joystickRegion;        // 左下：摇杆控制
        QRect statusRegion;          // 中下：状态信息
        QRect compassRegion;         // 右下：指南针
    };
    ControllerRegions regions;

    // 当前选择的无人机信息
    QString currentSelectedUAV = "B1";
    float currentSpeed = 0.0f;
    float flightTime = 0.0f;
    float flightDistance = 0.0f;
    int currentHeading = 0;  // 当前飞行方向（0-359度）

    // 绘制各个区域的函数
    void drawFriendlyInfoRegion(QPainter &painter);
    void drawRadarRegion(QPainter &painter);
    void drawEnemyInfoRegion(QPainter &painter);
    void drawJoystickRegion(QPainter &painter);
    void drawStatusRegion(QPainter &painter);
    void drawCompassRegion(QPainter &painter);

    // 计算区域位置
    void calculateRegions();

    // 雷达相关
    QTimer *radarSweepTimer;
    float radarSweepAngle = 0.0f;
    void updateRadarSweep();
};
#endif // MAINWINDOW_H
