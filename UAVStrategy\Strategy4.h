#ifndef STRATEGY4_H
#define STRATEGY4_H

#include <QVector>
#include <QPointF>
#include <QMap>
#include <QDateTime>

class Strategy4
{
public:
    Strategy4();
    QString log;

    // 计算无人机的速度向量（人工势场法）
    QPointF calculateVelocity(const QString& uavId, const QVector<float>& observation);

private:
    // 无人机行为模式枚举
    enum BehaviorMode {
        PATROL,      // 巡逻模式
        PURSUE,      // 追击模式
        INTERCEPT,   // 拦截模式
        EVADE,       // 逃避模式
        ESCAPE       // 逃离障碍模式
    };

    // 从观察向量中解析各类信息
    struct UAVInfo {
        QPointF position;
        float hp;
        QPointF velocity;  // 估计的速度
    };

    struct ObstacleInfo {
        QPointF position;
        float radius;
        bool isValid;
    };

    // 避障状态记录
    struct AvoidanceState {
        QVector<QPointF> recentPositions;         // 最近几个位置点
        QDateTime lastEscapeTime;                // 上次执行逃离的时间
        QPointF escapeDirection;                 // 逃离方向
        QPointF lastObstaclePos;                 // 上次检测到的最近障碍物位置
        bool inEscapeMode;                       // 是否处于逃离模式
        int escapeCountdown;                     // 逃离倒计时
        float preferredSide;                     // 优先绕行方向（左或右，1或-1）
        BehaviorMode mode;                       // 当前行为模式
        int currentTargetIndex;                  // 当前目标索引
        QDateTime lastTargetChangeTime;          // 上次更换目标的时间
        QPointF lastEnemyPosition;               // 上次记录的敌机位置
    };

    // 定义常量 - 全能战士策略：平衡性和适应性参数
    const float ATTRACTIVE_FORCE = 1.5f;        // 引力系数（平衡调整，提高响应性）
    const float REPULSIVE_FORCE = 5500.0f;      // 斥力系数（平衡调整，避免过度保守）
    const float OBSTACLE_INFLUENCE = 200.0f;    // 障碍物影响范围（适中，保持灵活性）
    const float ENEMY_INFLUENCE = 180.0f;       // 敌机影响范围
    const float MAX_VELOCITY = 48.0f;           // 最大速度限制（略降，提高控制精度）
    const float MIN_VELOCITY = 35.0f;           // 最小期望速度（降低，增强低速机动能力）
    const float MAP_WIDTH = 1280.0f;            // 地图宽度
    const float MAP_HEIGHT = 800.0f;            // 地图高度
    const float BOUNDARY_FORCE = 2000.0f;       // 边界斥力系数
    const float BOUNDARY_DISTANCE = 80.0f;      // 边界影响距离
    const float MOUNTAIN_INFLUENCE = 200.0f;    // 山体影响范围（调整适应扩大半径）
    const float RADAR_INFLUENCE = 200.0f;       // 雷达影响范围（调整适应扩大半径）
    const float CLOUD_INFLUENCE = 250.0f;       // 雷云影响范围（调整适应扩大半径）
    const float OBSTACLE_SCALE = 3.5f;          // 障碍物斥力缩放（增强）
    const float TANGENTIAL_FORCE = 6.5f;        // 切向力系数（大幅增强）
    const int OSCILLATION_WINDOW = 5;           // 震荡检测窗口大小
    const float OSCILLATION_THRESHOLD = 20.0f;  // 震荡检测阈值
    const float ESCAPE_FORCE = 35.0f;           // 逃离力大小（增强）
    const float SAFETY_DISTANCE = 30.0f;        // 安全距离余量
    const float TARGET_SWITCH_TIME = 3000.0f;   // 目标切换最小间隔时间(ms)
    const float PREDICTION_FACTOR = 1.5f;       // 敌机位置预测系数
    const int COORDINATION_RANGE = 400;         // 协同作战范围
    const float HP_WEIGHT = 0.3f;               // HP在目标评分中的权重
    const float DISTANCE_WEIGHT = 0.7f;         // 距离在目标评分中的权重

    // 计算引力（目标吸引力）
    QPointF calculateAttractiveForce(const QPointF& currentPos, const QPointF& targetPos);

    // 计算斥力（障碍物和敌机的排斥力）
    QPointF calculateRepulsiveForce(const QPointF& currentPos, const QPointF& obstaclePos, float influenceRange, float obstacleRadius = 0.0f);

    // 计算切向力（用于沿障碍物表面滑行）
    QPointF calculateTangentialForce(const QPointF& currentPos, const QPointF& obstaclePos,
                                   float influenceRange, float obstacleRadius = 0.0f,
                                   const QString& uavId = QString(),
                                   const QPointF& targetPos = QPointF(0, 0));

    // 计算边界斥力（防止无人机飞出地图）
    QPointF calculateBoundaryForce(const QPointF& currentPos);

    // 检测是否陷入震荡状态
    bool detectOscillation(const QString& uavId, const QPointF& currentPos);

    // 计算逃离力（当陷入局部最小值时使用）
    QPointF calculateEscapeForce(const QString& uavId, const QPointF& currentPos, const QPointF& obstaclePos);

    // 向量归一化
    QPointF normalizeVector(const QPointF& vector);

    // 向量限制长度
    QPointF limitVectorLength(const QPointF& vector, float maxLength);

    // 确保向量长度达到最小速度要求
    QPointF ensureMinimumSpeed(const QPointF& vector, float minLength);

    // 选择最佳目标 - 考虑距离、血量和队友目标
    int selectBestTarget(const QString& uavId, const QPointF& currentPos,
                        const QVector<UAVInfo>& redUAVs, const QVector<UAVInfo>& blueUAVs);

    // 预测敌机位置
    QPointF predictEnemyPosition(const UAVInfo& enemy, const QString& uavId);

    // 全能战士策略：环境适应性方法
    float evaluateEnvironmentComplexity(const ObstacleInfo& mountain, const ObstacleInfo& radar,
                                       const ObstacleInfo& cloud, int enemyCount);
    void adaptParametersToEnvironment(const QString& uavId, float complexity);

    QVector<UAVInfo> parseBlueUAVs(const QVector<float>& observation);
    QVector<UAVInfo> parseRedUAVs(const QVector<float>& observation);
    ObstacleInfo parseMountainInfo(const QVector<float>& observation);
    ObstacleInfo parseRadarInfo(const QVector<float>& observation);
    ObstacleInfo parseCloudInfo(const QVector<float>& observation);

    // 存储上一次的速度向量，用于平滑处理
    QMap<QString, QPointF> lastVelocities;

    // 存储各无人机的避障状态
    QMap<QString, AvoidanceState> avoidanceStates;

    // 存储上一次观测的红方无人机信息
    QMap<int, UAVInfo> lastRedUAVStates;

    // 存储团队目标信息
    QMap<int, int> teamTargets;  // <红方索引, 蓝方计数>
};

#endif // STRATEGY4_H
