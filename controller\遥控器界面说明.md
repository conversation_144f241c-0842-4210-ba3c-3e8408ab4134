# 六区域遥控器界面设计说明

## 🎮 界面布局

新的遥控器界面被重新设计为六个功能区域，采用3x2的网格布局：

```
┌─────────────┬─────────────┬─────────────┐
│   左上区    │   中上区    │   右上区    │
│  我方信息   │  模拟雷达   │  敌方信息   │
└─────────────┼─────────────┼─────────────┤
│   左下区    │   中下区    │   右下区    │
│  摇杆控制   │  状态信息   │   指南针    │
└─────────────┴─────────────┴─────────────┘
```

## 📊 各区域功能详解

### 🔵 左上区域 - 我方信息
**功能**：显示我方无人机的实时状态
- **显示内容**：
  - 无人机ID（B1, B2, B3）
  - 血量值和血量条
  - 当前位置坐标
  - 当前选择的无人机高亮显示
- **颜色方案**：青色边框，蓝色背景
- **交互**：无直接交互，信息自动更新

### 🟢 中上区域 - 模拟雷达
**功能**：显示当前无人机探测到的周围环境
- **显示内容**：
  - 圆形雷达显示屏
  - 旋转扫描线（动画效果）
  - 敌方目标点（红色圆点）
  - 目标距离和方位
  - 雷达网格线
- **颜色方案**：绿色边框，深绿色背景
- **更新频率**：100ms刷新一次扫描线

### 🔴 右上区域 - 敌方信息
**功能**：显示敌方无人机的状态信息
- **显示内容**：
  - 敌方无人机ID（R1, R2, R3）
  - 血量值和血量条
  - 位置坐标
  - 与当前选择无人机的距离
- **颜色方案**：红色边框，深红色背景
- **交互**：无直接交互，信息自动更新

### 🟡 左下区域 - 摇杆控制
**功能**：控制当前选择无人机的飞行方向
- **显示内容**：
  - 圆形摇杆背景
  - 可拖拽的摇杆指针
  - 方向指示线
  - 角度和强度显示
- **颜色方案**：黄色边框，深黄色背景
- **交互方式**：
  - 鼠标拖拽控制方向
  - 键盘方向键控制
  - 松开自动回中

### 🟣 中下区域 - 状态信息
**功能**：显示和控制当前无人机的详细状态
- **显示内容**：
  - 无人机选择按钮（B1, B2, B3）
  - 当前速度
  - 飞行时间
  - 飞行距离
  - 无人机状态
- **颜色方案**：紫色边框，深紫色背景
- **交互方式**：点击按钮切换控制的无人机

### ⚪ 右下区域 - 指南针
**功能**：显示当前无人机的飞行方向
- **显示内容**：
  - 圆形指南针
  - 主要方向标记（N, E, S, W）
  - 红色方向指针
  - 数字方向角度
  - 当前速度显示
- **颜色方案**：白色边框，深灰色背景
- **更新方式**：根据无人机速度向量实时计算

## 🎯 交互功能

### 无人机选择
- **方式1**：点击中下区域的无人机按钮（B1, B2, B3）
- **方式2**：使用原有的下拉菜单（保持兼容性）
- **效果**：
  - 左上区域高亮显示选择的无人机
  - 雷达以选择的无人机为中心
  - 指南针显示选择无人机的方向
  - 状态信息更新为选择的无人机

### 飞行控制
- **摇杆控制**：
  - 鼠标拖拽摇杆指针
  - 角度范围：0-359度
  - 强度范围：0-100%
  - 实时显示角度和强度
- **键盘控制**：
  - 方向键控制摇杆位置
  - 自动回中机制

### 信息显示
- **实时更新**：所有信息每50ms更新一次
- **颜色编码**：
  - 绿色：健康状态（血量>60%）
  - 黄色：警告状态（血量30-60%）
  - 红色：危险状态（血量<30%）
- **距离计算**：自动计算并显示与敌方的距离

## 🔧 技术实现

### 区域计算
```cpp
void calculateRegions() {
    int margin = 30;
    int regionWidth = (width() - 4 * margin) / 3;
    int regionHeight = (height() - 3 * margin) / 2;
    
    // 3x2网格布局
    regions.friendlyInfoRegion = QRect(margin, margin, regionWidth, regionHeight);
    regions.radarRegion = QRect(margin * 2 + regionWidth, margin, regionWidth, regionHeight);
    // ... 其他区域
}
```

### 雷达扫描动画
```cpp
void updateRadarSweep() {
    radarSweepAngle += 6.0f; // 每次增加6度
    if (radarSweepAngle >= 360.0f) {
        radarSweepAngle = 0.0f;
    }
    update(regions.radarRegion); // 只更新雷达区域
}
```

### 指南针方向计算
```cpp
// 根据速度向量计算飞行方向
float vx = info.vx;
float vy = info.vy;
if (vx != 0 || vy != 0) {
    currentHeading = static_cast<int>(atan2(vx, -vy) * 180.0f / M_PI);
    if (currentHeading < 0) currentHeading += 360;
}
```

## 🎨 视觉设计

### 颜色方案
- **我方信息**：青色系（#00FFFF）
- **雷达显示**：绿色系（#00FF00）
- **敌方信息**：红色系（#FF0000）
- **摇杆控制**：黄色系（#FFFF00）
- **状态信息**：紫色系（#FF00FF）
- **指南针**：白色系（#FFFFFF）

### 字体设置
- **标题**：Arial 12pt Bold
- **内容**：Arial 10pt Regular
- **小字**：Arial 8pt Regular

### 动画效果
- **雷达扫描**：6度/100ms的旋转速度
- **血量条**：平滑的颜色渐变
- **选择高亮**：半透明黄色背景

## 📱 响应式设计

界面支持窗口大小调整：
- 区域大小自动计算
- 保持3x2布局比例
- 最小窗口尺寸：600x400像素
- 字体和控件大小自适应

## 🔄 数据更新机制

- **游戏数据**：通过QVSOA协议接收
- **UI更新**：50ms周期性更新
- **雷达扫描**：100ms动画更新
- **用户交互**：实时响应

这个新的六区域设计大大提升了遥控器的功能性和用户体验，提供了更直观、更丰富的信息显示和控制方式。
