#include "Strategy4.h"
#include <QtMath>
#include <QDebug>
#include <QRandomGenerator>
#include <QLineF>
#include <limits>
#include <algorithm>

Strategy4::Strategy4()
{
    // 初始化团队目标
    teamTargets.clear();
}

QPointF Strategy4::calculateVelocity(const QString& uavId, const QVector<float>& observation)
{
    // 从观察向量中解析出我方无人机和敌方无人机的信息
    QVector<UAVInfo> blueUAVs = parseBlueUAVs(observation);
    QVector<UAVInfo> redUAVs = parseRedUAVs(observation);

    // 解析障碍物信息
    ObstacleInfo mountainInfo = parseMountainInfo(observation);
    ObstacleInfo radarInfo = parseRadarInfo(observation);
    ObstacleInfo cloudInfo = parseCloudInfo(observation);

    // 找到对应ID的蓝方无人机
    int uavIndex = uavId.mid(1).toInt() - 1;  // 从B1,B2,B3提取索引(0,1,2)
    if (uavIndex < 0 || uavIndex >= blueUAVs.size() || blueUAVs[uavIndex].hp <= 0) {
        // 无人机不存在或已坠毁
        return QPointF(0, 0);
    }

    QPointF currentPos = blueUAVs[uavIndex].position;
    QPointF resultForce(0, 0);
    QPointF targetPos;

    // 初始化避障状态（如果不存在）
    if (!avoidanceStates.contains(uavId)) {
        AvoidanceState state;
        state.recentPositions.clear();
        state.lastEscapeTime = QDateTime::currentDateTime().addSecs(-60);  // 初始时间设为1分钟前
        state.escapeDirection = QPointF(0, 0);
        state.inEscapeMode = false;
        state.escapeCountdown = 0;
        state.mode = PATROL; // 初始设为巡逻模式
        state.currentTargetIndex = -1;
        state.lastTargetChangeTime = QDateTime::currentDateTime().addSecs(-60);
        state.lastEnemyPosition = QPointF(0, 0);

        // 根据无人机ID给每架无人机固定的绕行方向，减少随机性导致的震荡
        if (uavId == "B1") {
            state.preferredSide = 1.0f;  // B1向右绕行
        } else if (uavId == "B2") {
            state.preferredSide = -1.0f;  // B2向左绕行
        } else {
            state.preferredSide = 1.0f;  // B3向右绕行
        }

        avoidanceStates[uavId] = state;
    }

    // 更新位置历史
    AvoidanceState& state = avoidanceStates[uavId];
    state.recentPositions.append(currentPos);
    if (state.recentPositions.size() > OSCILLATION_WINDOW) {
        state.recentPositions.removeFirst();
    }

    // 全能战士策略：动态环境评估和参数自适应
    float environmentComplexity = evaluateEnvironmentComplexity(mountainInfo, radarInfo, cloudInfo, redUAVs.size());
    adaptParametersToEnvironment(uavId, environmentComplexity);

    // 寻找最佳目标红方无人机
    int targetIndex = selectBestTarget(uavId, currentPos, redUAVs, blueUAVs);
    bool hasTarget = (targetIndex >= 0);

    if (hasTarget) {
        // 如果找到有效目标，使用预测的位置作为目标
        UAVInfo& targetEnemy = redUAVs[targetIndex];

        // 记录敌机上次位置，用于后续速度计算
        state.lastEnemyPosition = targetEnemy.position;

        // 预测敌机位置作为目标
        targetPos = predictEnemyPosition(targetEnemy, uavId);

        // 根据距离自动切换行为模式
        float distance = qSqrt(qPow(targetEnemy.position.x() - currentPos.x(), 2) +
                             qPow(targetEnemy.position.y() - currentPos.y(), 2));

        // 300像素内进入拦截模式，积极追击
        if (distance < 300) {
            state.mode = INTERCEPT;
        } else {
            state.mode = PURSUE;
        }
    } else {
        state.mode = PATROL;
        state.currentTargetIndex = -1;

        // 获取蓝方无人机在场的数量
        int activeBlueCount = 0;
        for (const UAVInfo& blue : blueUAVs) {
            if (blue.hp > 0) activeBlueCount++;
        }

        // 获取当前无人机的ID编号
         int uavNumber = uavId.mid(1).toInt();

         // 边界安全距离
         qreal borderMargin = 200.0f;

         // 根据剩余无人机数量设置不同的巡逻策略
         if (activeBlueCount == 3) {
             // 三架无人机时，呈伞状分布
             if (uavNumber == 1) {
                 // B1向左巡逻
                 if (currentPos.x() <= borderMargin) {
                     // 到达左边界，改为向上
                     targetPos = QPointF(currentPos.x(), qMax(borderMargin, currentPos.y() - 2*MAX_VELOCITY));
                 } else {
                     targetPos = QPointF(qMax(borderMargin, currentPos.x() - 2*MAX_VELOCITY), currentPos.y());
                 }
             } else if (uavNumber == 2) {
                 // B2向上巡逻
                 if (currentPos.y() <= borderMargin) {
                     // 到达上边界，改为向左
                     targetPos = QPointF(qMax(borderMargin, currentPos.x() - 2*MAX_VELOCITY), currentPos.y());
                 } else {
                     targetPos = QPointF(currentPos.x(), qMax(borderMargin, currentPos.y() - 2*MAX_VELOCITY));
                 }
             } else if (uavNumber == 3) {
                 // B3向左上方巡逻
                 if (currentPos.x() <= borderMargin || currentPos.y() <= borderMargin) {
                     // 到达边界，改为向右下方
                     targetPos = QPointF(
                         qMin(MAP_WIDTH - borderMargin, currentPos.x() + 2*MAX_VELOCITY),
                         qMin(MAP_HEIGHT - borderMargin, currentPos.y() + 2*MAX_VELOCITY)
                     );
                 } else {
                     targetPos = QPointF(
                         qMax(borderMargin, currentPos.x() - 2*MAX_VELOCITY),
                         qMax(borderMargin, currentPos.y() - 2*MAX_VELOCITY)
                     );
                 }
             }
         } else if (activeBlueCount == 2) {
             // 两架无人机时，沿着矩形轨迹巡逻
             // 获取当前存活的无人机编号
             QVector<int> activeUAVNumbers;
             for (int i = 0; i < blueUAVs.size(); i++) {
                 if (blueUAVs[i].hp > 0) {
                     activeUAVNumbers.append(i + 1); // 转换为1-based索引
                 }
             }

             // 确保有两个编号
             if (activeUAVNumbers.size() == 2) {
                 // 第一个存活的无人机在左下方巡逻
                 if (uavNumber == activeUAVNumbers[0]) {
                     // 左下方矩形轨迹
                     if (currentPos.x() <= borderMargin && currentPos.y() < MAP_HEIGHT - borderMargin) {
                         // 左边界，向下移动
                         targetPos = QPointF(currentPos.x(), qMin(MAP_HEIGHT - borderMargin, currentPos.y() + 2*MAX_VELOCITY));
                     } else if (currentPos.y() >= MAP_HEIGHT - borderMargin && currentPos.x() < MAP_WIDTH/2) {
                         // 下边界，向右移动
                         targetPos = QPointF(qMin(static_cast<qreal>(MAP_WIDTH/2), currentPos.x() + 2*MAX_VELOCITY), currentPos.y());
                     } else if (currentPos.x() >= MAP_WIDTH/2 && currentPos.y() > borderMargin) {
                         // 右边界，向上移动
                         targetPos = QPointF(currentPos.x(), qMax(borderMargin, currentPos.y() - 2*MAX_VELOCITY));
                     } else {
                         // 上边界，向左移动
                         targetPos = QPointF(qMax(borderMargin, currentPos.x() - 2*MAX_VELOCITY), currentPos.y());
                     }
                 }
                 // 第二个存活的无人机在右上方巡逻
                 else if (uavNumber == activeUAVNumbers[1]) {
                     // 右上方矩形轨迹
                     if (currentPos.x() >= MAP_WIDTH - borderMargin && currentPos.y() > borderMargin) {
                         // 右边界，向上移动
                         targetPos = QPointF(currentPos.x(), qMax(borderMargin, currentPos.y() - 2*MAX_VELOCITY));
                     } else if (currentPos.y() <= borderMargin && currentPos.x() > MAP_WIDTH/2) {
                         // 上边界，向左移动
                         targetPos = QPointF(qMax(static_cast<qreal>(MAP_WIDTH/2), currentPos.x() - 2*MAX_VELOCITY), currentPos.y());
                     } else if (currentPos.x() <= MAP_WIDTH/2 && currentPos.y() < MAP_HEIGHT - borderMargin) {
                         // 左边界，向下移动
                         targetPos = QPointF(currentPos.x(), qMin(MAP_HEIGHT - borderMargin, currentPos.y() + 2*MAX_VELOCITY));
                     } else {
                         // 下边界，向右移动
                         targetPos = QPointF(qMin(MAP_WIDTH - borderMargin, currentPos.x() + 2*MAX_VELOCITY), currentPos.y());
                     }
                 }
             }
         } else if (activeBlueCount == 1) {
             // 一架无人机时，沿着矩形轨迹巡逻
             if (currentPos.x() <= borderMargin && currentPos.y() < MAP_HEIGHT - borderMargin) {
                 // 左边界，向下移动
                 targetPos = QPointF(currentPos.x(), qMin(MAP_HEIGHT - borderMargin, currentPos.y() + 2*MAX_VELOCITY));
             } else if (currentPos.y() >= MAP_HEIGHT - borderMargin && currentPos.x() < MAP_WIDTH - borderMargin) {
                 // 下边界，向右移动
                 targetPos = QPointF(qMin(MAP_WIDTH - borderMargin, currentPos.x() + 2*MAX_VELOCITY), currentPos.y());
             } else if (currentPos.x() >= MAP_WIDTH - borderMargin && currentPos.y() > borderMargin) {
                 // 右边界，向上移动
                 targetPos = QPointF(currentPos.x(), qMax(borderMargin, currentPos.y() - 2*MAX_VELOCITY));
             } else {
                 // 上边界，向左移动
                 targetPos = QPointF(qMax(borderMargin, currentPos.x() - 2*MAX_VELOCITY), currentPos.y());
             }
         } else {
             // 默认情况（不应该发生）
             targetPos = QPointF(MAP_WIDTH/2, MAP_HEIGHT/2);
         }
    }

    // 寻找最近的障碍物
    float minObstacleDistance = std::numeric_limits<float>::max();
    QPointF nearestObstaclePos;
    float nearestObstacleRadius = 0;
    bool foundObstacle = false;

    // 检查山脉
    if (mountainInfo.isValid) {
        QPointF repulsiveForce = calculateRepulsiveForce(currentPos, mountainInfo.position, MOUNTAIN_INFLUENCE, mountainInfo.radius) * OBSTACLE_SCALE;
        QPointF tangentialForce = calculateTangentialForce(currentPos, mountainInfo.position, MOUNTAIN_INFLUENCE, mountainInfo.radius, uavId, targetPos);
        resultForce += repulsiveForce + tangentialForce;

        // 更新最近障碍物信息
        float dx = currentPos.x() - mountainInfo.position.x();
        float dy = currentPos.y() - mountainInfo.position.y();
        float distance = qSqrt(dx*dx + dy*dy) - mountainInfo.radius;
        if (distance < minObstacleDistance) {
            minObstacleDistance = distance;
            nearestObstaclePos = mountainInfo.position;
            foundObstacle = true;
        }
    }

    // 检查雷达
    if (radarInfo.isValid) {
        QPointF repulsiveForce = calculateRepulsiveForce(currentPos, radarInfo.position, RADAR_INFLUENCE, radarInfo.radius) * OBSTACLE_SCALE;
        QPointF tangentialForce = calculateTangentialForce(currentPos, radarInfo.position, RADAR_INFLUENCE, radarInfo.radius, uavId, targetPos);
        resultForce += repulsiveForce + tangentialForce;

        // 更新最近障碍物信息
        float dx = currentPos.x() - radarInfo.position.x();
        float dy = currentPos.y() - radarInfo.position.y();
        float distance = qSqrt(dx*dx + dy*dy) - radarInfo.radius;
        if (distance < minObstacleDistance) {
            minObstacleDistance = distance;
            nearestObstaclePos = radarInfo.position;
            foundObstacle = true;
        }
    }

    // 检查雷云
    if (cloudInfo.isValid) {
        QPointF repulsiveForce = calculateRepulsiveForce(currentPos, cloudInfo.position, CLOUD_INFLUENCE, cloudInfo.radius) * OBSTACLE_SCALE * 1.5; // 雷云更危险
        QPointF tangentialForce = calculateTangentialForce(currentPos, cloudInfo.position, CLOUD_INFLUENCE, cloudInfo.radius, uavId, targetPos);
        resultForce += repulsiveForce + tangentialForce;

        // 更新最近障碍物信息
        float dx = currentPos.x() - cloudInfo.position.x();
        float dy = currentPos.y() - cloudInfo.position.y();
        float distance = qSqrt(dx*dx + dy*dy) - cloudInfo.radius;
        if (distance < minObstacleDistance) {
            minObstacleDistance = distance;
            nearestObstaclePos = cloudInfo.position;
            foundObstacle = true;
        }
    }

    // 更新最近障碍物信息
    if (foundObstacle) {
        state.lastObstaclePos = nearestObstaclePos;
    }

    // 检查是否陷入震荡
    bool isOscillating = detectOscillation(uavId, currentPos);

    // 紧急避障判断 - 降低阈值，提前启动避障
    bool emergencyAvoidance = minObstacleDistance < OBSTACLE_INFLUENCE * 0.8 + SAFETY_DISTANCE;

    // 根据当前状态选择行为模式
    if (state.inEscapeMode) {
        // 正在执行逃离模式
        if (state.escapeCountdown > 0) {
            state.escapeCountdown--;
            // 确保逃离速度接近最大速度
            resultForce = state.escapeDirection * MAX_VELOCITY * 0.95;
//            qDebug() << uavId << "正在执行逃离模式，剩余:" << state.escapeCountdown;
        } else {
            state.inEscapeMode = false;
//            qDebug() << uavId << "逃离模式结束";
        }
    } else if (isOscillating && foundObstacle) {
        // 检测到震荡，进入逃离模式
        state.mode = ESCAPE;
        state.inEscapeMode = true;
        state.escapeCountdown = 40; // 增加执行时间，确保完全绕过障碍物
        state.lastEscapeTime = QDateTime::currentDateTime();

        // 计算逃离方向（垂直于最近障碍物的方向，且偏向于当前速度）
        state.escapeDirection = calculateEscapeForce(uavId, currentPos, nearestObstaclePos);
        // 设置接近最大速度的逃离力度
        resultForce = state.escapeDirection * MAX_VELOCITY * 0.95;

//        qDebug() << uavId << "检测到震荡！启动逃离模式";
    } else {
        // 正常模式：根据当前行为模式计算不同的力

        // 1. 计算目标引力
        float attractiveScale = 1.0f;

        // 根据模式调整引力比例
        if (emergencyAvoidance) {
            attractiveScale = 0.7f; // 紧急避障时降低引力
        } else if (state.mode == INTERCEPT) {
            attractiveScale = 1.5f; // 拦截模式增强引力
        } else if (state.mode == PURSUE) {
            attractiveScale = 1.2f; // 追击模式略微增强引力
        } else {
            attractiveScale = 1.0f; // 巡逻模式使用较小引力
        }

        QPointF attractiveForce = calculateAttractiveForce(currentPos, targetPos) * attractiveScale;
        resultForce += attractiveForce;

        // 2. 障碍物处理（斥力和切向力）已在上面处理

        // 3. 计算边界斥力
        QPointF boundaryForce = calculateBoundaryForce(currentPos);
        resultForce += boundaryForce;

        // 4. 计算其他蓝方无人机的斥力（避免碰撞）
        for (int i = 0; i < blueUAVs.size(); i++) {
            if (i == uavIndex || blueUAVs[i].hp <= 0) continue;  // 跳过自己和已坠毁的队友

            QPointF friendPos = blueUAVs[i].position;
            float distance = qSqrt(qPow(friendPos.x() - currentPos.x(), 2) +
                                qPow(friendPos.y() - currentPos.y(), 2));

            // 如果队友距离小于影响范围，计算斥力
            if (distance < OBSTACLE_INFLUENCE/2) {
                QPointF repulsiveForce = calculateRepulsiveForce(currentPos, friendPos, OBSTACLE_INFLUENCE/2);
                resultForce += repulsiveForce;  // 提高队友斥力权重
            }
        }

        // 5. 如果速度不足，在安全的方向上添加基础速度
        float resultMagnitude = qSqrt(resultForce.x() * resultForce.x() + resultForce.y() * resultForce.y());
        if (resultMagnitude < MIN_VELOCITY * 0.8f && !emergencyAvoidance) {
            // 如果有目标，朝目标方向添加基础速度
            if (hasTarget) {
                QPointF toTarget = normalizeVector(QPointF(targetPos.x() - currentPos.x(), targetPos.y() - currentPos.y()));
                resultForce += toTarget * MIN_VELOCITY;
            }
            // 否则根据当前朝向或ID分配方向
            else if (lastVelocities.contains(uavId) &&
                    (lastVelocities[uavId].x() != 0 || lastVelocities[uavId].y() != 0)) {
                QPointF direction = normalizeVector(lastVelocities[uavId]);
                resultForce += direction * MIN_VELOCITY * 0.9f;
            } else {
                // 根据ID分配默认方向
                if (uavId == "B1") {
                    resultForce += QPointF(1, 0) * MIN_VELOCITY * 0.9f;  // 向右
                } else if (uavId == "B2") {
                    resultForce += QPointF(0, -1) * MIN_VELOCITY * 0.9f; // 向上
                } else {
                    resultForce += QPointF(-1, 0) * MIN_VELOCITY * 0.9f; // 向左
                }
            }
        }
    }

    // 6. 限制速度大小
    QPointF velocity = limitVectorLength(resultForce, MAX_VELOCITY);

    // 7. 速度平滑处理（与上一次速度加权平均）
    if (lastVelocities.contains(uavId)) {
        QPointF lastVelocity = lastVelocities[uavId];

        // 根据模式调整平滑因子
        float smoothFactor = 0.2f;

        if (state.inEscapeMode || emergencyAvoidance) {
            smoothFactor = 0.1f; // 紧急情况下更快响应
        } else if (state.mode == INTERCEPT) {
            smoothFactor = 0.15f; // 拦截模式较低平滑因子，更快响应
        } else if (state.mode == PATROL) {
            smoothFactor = 0.25f; // 巡逻模式较高平滑因子，更平稳
        }

        velocity = QPointF((1 - smoothFactor) * velocity.x() + smoothFactor * lastVelocity.x(),
                          (1 - smoothFactor) * velocity.y() + smoothFactor * lastVelocity.y());
    }

    // 8. 确保速度不低于最小值（除非紧急避障或已经很接近目标）
    float distToTarget = 0;
    if (hasTarget) {
        distToTarget = qSqrt(qPow(targetPos.x() - currentPos.x(), 2) + qPow(targetPos.y() - currentPos.y(), 2));
    }

    // 只有在非紧急避障且距离目标足够远时，才确保最小速度
    if (!emergencyAvoidance && !state.inEscapeMode && (!hasTarget || distToTarget > 50)) {
        velocity = ensureMinimumSpeed(velocity, MIN_VELOCITY);
    }

    // 更新上一次速度
    lastVelocities[uavId] = velocity;

    // 检查速度是否过小，如果是则增加一点随机偏移避免卡住
    float velMagnitude = qSqrt(velocity.x() * velocity.x() + velocity.y() * velocity.y());
    if (velMagnitude < 5.0 && (emergencyAvoidance || isOscillating)) {
        double angle = QRandomGenerator::global()->generateDouble() * 2 * M_PI;
        QPointF randomDir(qCos(angle), qSin(angle));
        velocity += randomDir * 15.0; // 添加更强的随机方向速度
        velocity = limitVectorLength(velocity, MAX_VELOCITY);
//        qDebug() << uavId << "速度过小，添加随机扰动";
    }

    // 输出调试信息
//    qDebug() << "策略4：" << uavId
//             << " 位置:" << currentPos
//             << " 速度:" << velocity
//             << " 模式:" << (state.mode == PATROL ? "巡逻" :
//                                        state.mode == PURSUE ? "追击" :
//                                        state.mode == INTERCEPT ? "拦截" :
//                                        state.mode == EVADE ? "逃避" :
//                                        state.mode == ESCAPE ? "逃离" : "未知")
//             << " 目标:" << (hasTarget ? targetIndex : -1)
//             << " 目标点:"<<targetPos;

    // 日志组装
    log = QString("策略4：%1 位置:(%2,%3) 速度:(%4,%5) 模式:%6 目标:%7 目标点:(%8,%9)")
     .arg(uavId)
     .arg(currentPos.x()).arg(currentPos.y())
     .arg(velocity.x()).arg(velocity.y())
     .arg(state.mode == PATROL ? "巡逻" :
          state.mode == PURSUE ? "追击" :
          state.mode == INTERCEPT ? "拦截" :
          state.mode == EVADE ? "逃避" :
          state.mode == ESCAPE ? "逃离" : "未知")
     .arg(hasTarget ? QString::number(targetIndex) : QString("-1"))
     .arg(targetPos.x()).arg(targetPos.y());

    return velocity;
}

// 全能战士策略：评估环境复杂度
float Strategy4::evaluateEnvironmentComplexity(const ObstacleInfo& mountain, const ObstacleInfo& radar,
                                              const ObstacleInfo& cloud, int enemyCount)
{
    float complexity = 0.0f;

    // 障碍物复杂度评估
    if (mountain.isValid) {
        complexity += 3.0f;  // 山体是最大威胁
    }

    if (radar.isValid) {
        complexity += 2.0f;  // 雷达次之
    }

    if (cloud.isValid) {
        complexity += 2.5f;  // 雷云威胁较大且移动
    }

    // 敌机数量影响
    complexity += enemyCount * 1.5f;

    // 障碍物密集度（多个障碍物同时存在时增加复杂度）
    int obstacleCount = 0;
    if (mountain.isValid) obstacleCount++;
    if (radar.isValid) obstacleCount++;
    if (cloud.isValid) obstacleCount++;

    if (obstacleCount >= 2) {
        complexity += obstacleCount * 0.5f;  // 多障碍物协同威胁
    }

    return qMin(complexity, 10.0f);  // 限制最大复杂度为10
}

// 全能战士策略：根据环境复杂度自适应调整参数
void Strategy4::adaptParametersToEnvironment(const QString& uavId, float complexity)
{
    // 根据复杂度调整参数
    if (complexity <= 3.0f) {
        // 简单环境：更激进的参数
        // 使用默认参数，适度提高速度
        // 这里可以动态调整一些参数，但保持平衡特性
    }
    else if (complexity <= 6.0f) {
        // 中等复杂环境：平衡参数（默认设置）
        // 保持当前的平衡参数设置
    }
    else {
        // 复杂环境：更保守的参数
        // 降低速度，增加安全距离
        // 可以在这里调整避障参数，但保持全能特性
    }

    // 记录环境复杂度用于调试
    static QMap<QString, float> lastComplexity;
    if (!lastComplexity.contains(uavId) ||
        qAbs(lastComplexity[uavId] - complexity) > 1.0f) {
        lastComplexity[uavId] = complexity;
        // qDebug() << "Strategy4: 无人机" << uavId << "环境复杂度:" << complexity;
    }
}

QPointF Strategy4::calculateAttractiveForce(const QPointF& currentPos, const QPointF& targetPos)
{
    // 计算方向向量
    QPointF direction(targetPos.x() - currentPos.x(), targetPos.y() - currentPos.y());

    // 计算距离
    float distance = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());

    if (distance < 0.001) {
        return QPointF(0, 0);  // 避免除以零
    }

    // 归一化方向向量
    direction = normalizeVector(direction);

    // 修改引力计算：引力随着距离增加而增加，但永远不小于最小速度的60%
    float forceMagnitude = qMax(MIN_VELOCITY * 0.6f, qMin(distance * ATTRACTIVE_FORCE, MAX_VELOCITY));

    return QPointF(direction.x() * forceMagnitude, direction.y() * forceMagnitude);
}

QPointF Strategy4::calculateRepulsiveForce(const QPointF& currentPos, const QPointF& obstaclePos, float influenceRange, float obstacleRadius)
{
    QPointF direction = currentPos - obstaclePos;
    float distance = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());

    // 考虑障碍物半径
    distance = qMax(distance - obstacleRadius, 0.1f);  // 防止除零错误

    // 如果距离超出影响范围，不产生斥力
    if (distance > influenceRange) {
        return QPointF(0, 0);
    }

    // 归一化方向向量
    direction = normalizeVector(direction);

    // 计算斥力大小：与障碍物距离成反比，并考虑障碍物半径
    // 使用改进的公式，随距离减小，力增长更快
    float forceMagnitude = REPULSIVE_FORCE * (1.0f / distance - 1.0f / influenceRange) *
                          (1.0f / (distance * distance)) *
                          (1.0f + obstacleRadius / 50.0f);  // 考虑障碍物大小因素

    return direction * forceMagnitude;
}

QPointF Strategy4::calculateTangentialForce(const QPointF& currentPos, const QPointF& obstaclePos, float influenceRange, float obstacleRadius, const QString& uavId, const QPointF& targetPos)
{
    QPointF directionToObstacle = currentPos - obstaclePos;
    float distance = qSqrt(directionToObstacle.x() * directionToObstacle.x() + directionToObstacle.y() * directionToObstacle.y());

    // 考虑障碍物半径
    distance = qMax(distance - obstacleRadius, 0.1f);  // 防止除零错误

    // 如果距离超出影响范围，不产生切向力
    if (distance > influenceRange) {
        return QPointF(0, 0);
    }

    // 归一化方向向量
    directionToObstacle = normalizeVector(directionToObstacle);

    // 计算切向向量（顺时针旋转90度）
    QPointF tangent(-directionToObstacle.y(), directionToObstacle.x());

    // 根据障碍物特性调整切向力方向
    if (!uavId.isEmpty() && avoidanceStates.contains(uavId)) {
        AvoidanceState& state = avoidanceStates[uavId];

        // 根据无人机行为模式决定切向力方向
        if (state.mode == PATROL || state.mode == EVADE || state.mode == ESCAPE) {
            // 巡逻模式、逃避模式和逃离模式使用固定方向
            tangent = tangent * state.preferredSide;
        }
        else if ((state.mode == PURSUE || state.mode == INTERCEPT) && targetPos != QPointF(0, 0)) {
            // 追击模式和拦截模式，根据敌机位置动态调整方向

            // 计算障碍物到目标的向量
            QPointF obstacleToTarget = targetPos - obstaclePos;

            // 计算两个可能的切向方向
            QPointF tangent1 = QPointF(-directionToObstacle.y(), directionToObstacle.x()); // 顺时针
            QPointF tangent2 = QPointF(directionToObstacle.y(), -directionToObstacle.x()); // 逆时针

            // 计算当前位置沿两个切向方向移动后与目标的距离
            QPointF pos1 = currentPos + tangent1 * 10; // 模拟沿tangent1方向移动
            QPointF pos2 = currentPos + tangent2 * 10; // 模拟沿tangent2方向移动

            float dist1 = qSqrt(qPow(pos1.x() - targetPos.x(), 2) + qPow(pos1.y() - targetPos.y(), 2));
            float dist2 = qSqrt(qPow(pos2.x() - targetPos.x(), 2) + qPow(pos2.y() - targetPos.y(), 2));

            // 选择使距离更小的方向
            if (dist1 < dist2) {
                tangent = tangent1; // 顺时针方向
            } else {
                tangent = tangent2; // 逆时针方向
            }

//            qDebug()<<"追击模式和拦截模式 计算切向方向"<<tangent;
        } else {
            // 其他情况使用固定方向
            tangent = tangent * state.preferredSide;
        }
    }

    // 计算切向力大小：使用指数关系，在接近障碍物时力量更大
    float normalizedDistance = distance / influenceRange;

    // 修改为非线性关系，距离越近，力量增加越快
    float forceFactor = qPow(1.0f - normalizedDistance, 2.0f);  // 平方关系，距离近时增加更快

    // 考虑障碍物大小因素，大障碍物产生更强的切向力
    float sizeFactor = 1.0f + obstacleRadius / 40.0f;

    // 在很近的距离时（小于影响范围的30%）进一步增加切向力
    if (normalizedDistance < 0.3f) {
        forceFactor *= 1.5f;  // 近距离额外增强50%
    }

    float forceMagnitude = TANGENTIAL_FORCE * forceFactor * sizeFactor;

    return tangent * forceMagnitude;
}

QPointF Strategy4::calculateBoundaryForce(const QPointF& currentPos)
{
    QPointF force(0, 0);

    // 左边界
    if (currentPos.x() < BOUNDARY_DISTANCE) {
        float magnitude = BOUNDARY_FORCE * qPow(1.0f - currentPos.x() / BOUNDARY_DISTANCE, 2);
        force += QPointF(magnitude, 0);
    }

    // 右边界
    if (currentPos.x() > MAP_WIDTH - BOUNDARY_DISTANCE) {
        float magnitude = BOUNDARY_FORCE * qPow(1.0f - (MAP_WIDTH - currentPos.x()) / BOUNDARY_DISTANCE, 2);
        force += QPointF(-magnitude, 0);
    }

    // 上边界
    if (currentPos.y() < BOUNDARY_DISTANCE) {
        float magnitude = BOUNDARY_FORCE * qPow(1.0f - currentPos.y() / BOUNDARY_DISTANCE, 2);
        force += QPointF(0, magnitude);
    }

    // 下边界
    if (currentPos.y() > MAP_HEIGHT - BOUNDARY_DISTANCE) {
        float magnitude = BOUNDARY_FORCE * qPow(1.0f - (MAP_HEIGHT - currentPos.y()) / BOUNDARY_DISTANCE, 2);
        force += QPointF(0, -magnitude);
    }

    return force;
}

bool Strategy4::detectOscillation(const QString& uavId, const QPointF& currentPos)
{
    if (!avoidanceStates.contains(uavId)) {
        return false;
    }

    AvoidanceState& state = avoidanceStates[uavId];

    // 需要至少有窗口大小数量的位置记录
    if (state.recentPositions.size() < OSCILLATION_WINDOW) {
        return false;
    }

    // 如果刚刚执行过逃离，等待更短的时间再检测
    // 缩短冷却时间以更快响应新的震荡
    if (state.lastEscapeTime.msecsTo(QDateTime::currentDateTime()) < 1000) {
        return false;
    }

    // 计算位置变化的总距离
    float totalDistance = 0;
    for (int i = 1; i < state.recentPositions.size(); i++) {
        QPointF p1 = state.recentPositions[i-1];
        QPointF p2 = state.recentPositions[i];
        float distance = qSqrt(qPow(p2.x() - p1.x(), 2) + qPow(p2.y() - p1.y(), 2));
        totalDistance += distance;
    }

    // 计算起点和终点之间的直线距离
    QPointF start = state.recentPositions.first();
    QPointF end = state.recentPositions.last();
    float directDistance = qSqrt(qPow(end.x() - start.x(), 2) + qPow(end.y() - start.y(), 2));

    // 检查是否有来回移动的特征（方向变化大）
    int directionChanges = 0;
    for (int i = 2; i < state.recentPositions.size(); i++) {
        QPointF v1 = state.recentPositions[i-1] - state.recentPositions[i-2];
        QPointF v2 = state.recentPositions[i] - state.recentPositions[i-1];

        // 计算两个移动向量的点积
        float dotProduct = v1.x() * v2.x() + v1.y() * v2.y();

        // 如果点积为负，说明方向发生了较大变化
        if (dotProduct < 0) {
            directionChanges++;
        }
    }

    // 综合判断震荡：路径效率低下或者方向变化频繁
    bool pathInefficient = (totalDistance > 0 && directDistance/totalDistance < 0.5);
    bool frequentChanges = (directionChanges >= 2);

    // 如果路径效率低下且总距离超过阈值，或者方向变化频繁，判定为震荡
    if ((pathInefficient && totalDistance > OSCILLATION_THRESHOLD) || frequentChanges) {
//        qDebug() << uavId << "检测到震荡: 总距离=" << totalDistance
//                << " 直线距离=" << directDistance
//                << " 方向变化=" << directionChanges;
        return true;
    }

    return false;
}

QPointF Strategy4::calculateEscapeForce(const QString& uavId, const QPointF& currentPos, const QPointF& obstaclePos)
{
    if (!avoidanceStates.contains(uavId)) {
        return QPointF(1, 0);  // 默认向右逃离
    }

    AvoidanceState& state = avoidanceStates[uavId];

    // 计算从障碍物到无人机的向量
    QPointF toUav = currentPos - obstaclePos;

    // 计算逃离方向（垂直于径向方向）
    QPointF tangent;

    if (toUav.x() == 0 && toUav.y() == 0) {
        // 如果重合，选择一个随机方向
        double angle = QRandomGenerator::global()->generateDouble() * 2 * M_PI;
        tangent = QPointF(qCos(angle), qSin(angle));
    } else {
        // 垂直于径向的切向方向，使用预设的方向偏好
        float mag = qSqrt(toUav.x() * toUav.x() + toUav.y() * toUav.y());

        // 使用预设的绕行方向
        if (state.preferredSide > 0) {
            tangent = QPointF(-toUav.y() / mag, toUav.x() / mag);  // 顺时针
        } else {
            tangent = QPointF(toUav.y() / mag, -toUav.x() / mag);  // 逆时针
        }
    }

    // 计算目标方向（假设有目标）
    QPointF targetDirection(1, 0);  // 默认向右

    // 如果存在上一次速度，用它作为参考
    if (lastVelocities.contains(uavId)) {
        QPointF lastVel = lastVelocities[uavId];
        if (lastVel.x() != 0 || lastVel.y() != 0) {
            float mag = qSqrt(lastVel.x() * lastVel.x() + lastVel.y() * lastVel.y());
            targetDirection = QPointF(lastVel.x() / mag, lastVel.y() / mag);
        }
    }

    // 将逃离方向稍微偏向当前速度方向，使避障更自然
    float dotProduct = tangent.x() * targetDirection.x() + tangent.y() * targetDirection.y();

    // 如果切向与当前方向基本相同，直接使用切向
    // 否则，混合切向和当前方向
    QPointF escapeDir;
    if (dotProduct > 0.7) {
        escapeDir = tangent; // 方向接近，保持纯粹的切向
    } else {
        // 混合切向和当前方向，但确保切向为主
        escapeDir = QPointF(
            0.8 * tangent.x() + 0.2 * targetDirection.x(),
            0.8 * tangent.y() + 0.2 * targetDirection.y()
        );
    }

    // 有5%概率随机改变方向，打破对称性
    if (QRandomGenerator::global()->generateDouble() < 0.05) {
        escapeDir = QPointF(-escapeDir.x(), -escapeDir.y());
//        qDebug() << uavId << "随机改变逃离方向";
    }

    // 添加一点随机扰动（较小幅度，避免过多随机性）
    double angle = QRandomGenerator::global()->generateDouble() * M_PI / 8 - M_PI / 16;  // -11.25~+11.25度
    float cos_a = qCos(angle);
    float sin_a = qSin(angle);
    escapeDir = QPointF(
        escapeDir.x() * cos_a - escapeDir.y() * sin_a,
        escapeDir.x() * sin_a + escapeDir.y() * cos_a
    );

    return normalizeVector(escapeDir);
}

QPointF Strategy4::ensureMinimumSpeed(const QPointF& vector, float minLength)
{
    float length = qSqrt(vector.x() * vector.x() + vector.y() * vector.y());

    // 如果速度太低且方向有效，增加速度
    if ((length < minLength) && (length > 0.1f)) {
        float scaleFactor = minLength / length;
        return QPointF(vector.x() * scaleFactor, vector.y() * scaleFactor);
    }

    return vector;
}

QPointF Strategy4::normalizeVector(const QPointF& vector)
{
    float length = qSqrt(vector.x() * vector.x() + vector.y() * vector.y());

    if (length < 0.001) {
        return QPointF(0, 0);  // 避免除以零
    }

    return QPointF(vector.x() / length, vector.y() / length);
}

QPointF Strategy4::limitVectorLength(const QPointF& vector, float maxLength)
{
    float length = qSqrt(vector.x() * vector.x() + vector.y() * vector.y());

    if (length <= maxLength || length < 0.001) {
        return vector;
    }

    return QPointF(vector.x() * maxLength / length, vector.y() * maxLength / length);
}

QVector<Strategy4::UAVInfo> Strategy4::parseBlueUAVs(const QVector<float>& observation)
{
    QVector<UAVInfo> blueUAVs;

    // 前9个元素是蓝方无人机信息，每3个一组 (x, y, hp)
    for (int i = 0; i < 3; i++) {
        UAVInfo info;
        int baseIndex = i * 3;

        info.position = QPointF(observation[baseIndex] , observation[baseIndex + 1] );  // 转为像素坐标
        info.hp = observation[baseIndex + 2];

        blueUAVs.append(info);
    }

    return blueUAVs;
}

QVector<Strategy4::UAVInfo> Strategy4::parseRedUAVs(const QVector<float>& observation)
{
    QVector<UAVInfo> redUAVs;

    // 9-17元素是红方无人机信息，每3个一组 (x, y, hp)
    for (int i = 0; i < 3; i++) {
        UAVInfo info;
        int baseIndex = 9 + i * 3;

        // 如果坐标是-1（未探测到），则设为(0,0)
        if (observation[baseIndex] < 0 || observation[baseIndex + 1] < 0) {
            info.position = QPointF(0, 0);
        } else {
            info.position = QPointF(observation[baseIndex] , observation[baseIndex + 1] );  // 转为像素坐标
        }

        info.hp = observation[baseIndex + 2] < 0 ? 0 : observation[baseIndex + 2];
        info.velocity = QPointF(0, 0); // 初始速度为零

        // 计算速度：基于上次观测的位置估算
        if (lastRedUAVStates.contains(i) && lastRedUAVStates[i].position.x() != 0 &&
            lastRedUAVStates[i].position.y() != 0 && info.position.x() != 0 && info.position.y() != 0) {
            QPointF lastPos = lastRedUAVStates[i].position;
            info.velocity = QPointF(info.position.x() - lastPos.x(), info.position.y() - lastPos.y());
        }

        redUAVs.append(info);

        // 更新上次观测状态
        lastRedUAVStates[i] = info;
    }

    return redUAVs;
}

// 选择最佳目标
int Strategy4::selectBestTarget(const QString& uavId, const QPointF& currentPos,
                               const QVector<UAVInfo>& redUAVs, const QVector<UAVInfo>& blueUAVs)
{
    int bestTargetIndex = -1;
    float bestScore = -1;

    // 检查避障状态中的当前目标是否仍然有效
    if (avoidanceStates.contains(uavId)) {
        AvoidanceState& state = avoidanceStates[uavId];

        // 如果有当前目标且时间间隔不够长，继续使用相同目标
        if (state.currentTargetIndex >= 0 && state.currentTargetIndex < redUAVs.size() &&
            state.lastTargetChangeTime.msecsTo(QDateTime::currentDateTime()) < TARGET_SWITCH_TIME) {

            // 确保目标仍然有效（可见且未坠毁）
            if (redUAVs[state.currentTargetIndex].position.x() > 0 &&
                redUAVs[state.currentTargetIndex].position.y() > 0 &&
                redUAVs[state.currentTargetIndex].hp > 0) {
                return state.currentTargetIndex;
            }
        }
    }

    // 获取蓝方无人机在场的数量
    int activeBlueCount = 0;
    for (const UAVInfo& blue : blueUAVs) {
        if (blue.hp > 0) activeBlueCount++;
    }

    // 统计目标分配情况
    teamTargets.clear();
    for (const auto& item : avoidanceStates.keys()) {
        if (item == uavId) continue;
        int idx = item.mid(1).toInt() - 1;
        if ((idx >= 0) && (idx < blueUAVs.size()) && (blueUAVs[idx].hp > 0)) {
            int targetIdx = avoidanceStates[item].currentTargetIndex;
            if (targetIdx >= 0 && targetIdx < redUAVs.size() &&
                (redUAVs[targetIdx].hp > 0 )&&
                (redUAVs[targetIdx].position.x() > 0 && redUAVs[targetIdx].position.y() > 0)) {
                teamTargets[targetIdx] = teamTargets.value(targetIdx, 0) + 1;
            }
        }
    }

    // 评估每个红方无人机的分数
    for (int i = 0; i < redUAVs.size(); i++) {
        // 跳过无效目标
        if (redUAVs[i].hp <= 0 || redUAVs[i].position.x() == 0 && redUAVs[i].position.y() == 0) {
            continue;
        }

        // 计算距离分数（距离越近分数越高）
        float distance = qSqrt(qPow(redUAVs[i].position.x() - currentPos.x(), 2) +
                              qPow(redUAVs[i].position.y() - currentPos.y(), 2));
        float distScore = 1.0f - (distance / (MAP_WIDTH + MAP_HEIGHT)); // 归一化距离分数

        // 计算HP分数（HP越低分数越高）
        float hpScore = 1.0f - (redUAVs[i].hp / 100.0f);

        // 协同作战考虑
        float teamFactor = 1.0f;
        int targetCount = teamTargets.value(i, 0);

        // 如果敌机已经有无人机在追击，根据队友数量降低评分
        if (targetCount > 0) {
            // 避免所有无人机都追一个目标
            if (activeBlueCount > 1 && targetCount >= activeBlueCount - 1) {
                teamFactor = 0.3f;  // 如果已经有足够无人机在追这个目标，大幅降低分数
            } else {
                teamFactor = 1.0f - (targetCount * 0.2f); // 每多一个队友追击，降低20%分数
            }
        } else {
            teamFactor = 1.2f; // 如果没有队友追击，给予奖励
        }

        // 计算最终分数
        float score = (DISTANCE_WEIGHT * distScore + HP_WEIGHT * hpScore) * teamFactor;

        if (score > bestScore) {
            bestScore = score;
            bestTargetIndex = i;
        }
    }

    // 如果找到目标，更新状态
    if (bestTargetIndex >= 0 && avoidanceStates.contains(uavId)) {
        AvoidanceState& state = avoidanceStates[uavId];
        bool targetChanged = (state.currentTargetIndex != bestTargetIndex);

        state.currentTargetIndex = bestTargetIndex;
        if (targetChanged) {
            state.lastTargetChangeTime = QDateTime::currentDateTime();
            state.mode = PURSUE; // 设置为追击模式
        }
    }

    return bestTargetIndex;
}

// 预测敌机位置
QPointF Strategy4::predictEnemyPosition(const UAVInfo& enemy, const QString& uavId)
{
    // 如果无人机没有检测到速度，直接返回当前位置
    if (enemy.velocity.x() == 0 && enemy.velocity.y() == 0) {
        return enemy.position;
    }

    // 获取敌机当前位置和估计速度
    QPointF currentPos = enemy.position;
    QPointF velocity = enemy.velocity;

    // 计算无人机到敌机的距离
    float distance = 0;
    if (avoidanceStates.contains(uavId)) {
        QPointF uavPos = avoidanceStates[uavId].recentPositions.last();
        distance = qSqrt(qPow(currentPos.x() - uavPos.x(), 2) + qPow(currentPos.y() - uavPos.y(), 2));
    }

    // 根据距离调整预测因子：距离越远，预测时间越长
    float predictionTime = qMin(distance / MAX_VELOCITY * 0.5f, 2.0f); // 最多预测2秒

    // 计算预测位置
    QPointF predictedPos = QPointF(
        currentPos.x() + velocity.x() * predictionTime * PREDICTION_FACTOR,
        currentPos.y() + velocity.y() * predictionTime * PREDICTION_FACTOR
    );

    // 确保预测位置在地图内
    predictedPos.setX(qBound(0.0f, (float)predictedPos.x(), MAP_WIDTH));
    predictedPos.setY(qBound(0.0f, (float)predictedPos.y(), MAP_HEIGHT));

    return predictedPos;
}

Strategy4::ObstacleInfo Strategy4::parseMountainInfo(const QVector<float>& observation)
{
    ObstacleInfo info;

    // 山脉信息在18-20索引位置
    float x = observation[18];
    float y = observation[19];
    float radius = observation[20];

    if (x <= 0 && y <= 0) {
        // 未检测到山脉
        info.position = QPointF(0, 0);
        info.radius = 0;
        info.isValid = false;
    } else {
        info.position = QPointF(x , y );  // 转为像素坐标
        info.radius = radius  + 10;  // 原始半径基础上增加10px安全距离
        info.isValid = true;
//        qDebug() << "山脉实际半径:" << radius  << "，避障半径:" << info.radius;
    }

    return info;
}

Strategy4::ObstacleInfo Strategy4::parseRadarInfo(const QVector<float>& observation)
{
    ObstacleInfo info;

    // 雷达信息在21-23索引位置
    float x = observation[21];
    float y = observation[22];
    float radius = observation[23];

    if (x <= 0 && y <= 0) {
        // 未检测到雷达
        info.position = QPointF(0, 0);
        info.radius = 0;
        info.isValid = false;
    } else {
        info.position = QPointF(x , y );  // 转为像素坐标
        info.radius = radius  + 10;  // 原始半径基础上增加10px安全距离
        info.isValid = true;
//        qDebug() << "雷达实际半径:" << radius  << "，避障半径:" << info.radius;
    }

    return info;
}

Strategy4::ObstacleInfo Strategy4::parseCloudInfo(const QVector<float>& observation)
{
    ObstacleInfo info;

    // 雷云信息在24-26索引位置
    float x = observation[24];
    float y = observation[25];
    float radius = observation[26];

    if (x <= 0 && y <= 0) {
        // 未检测到雷云
        info.position = QPointF(0, 0);
        info.radius = 0;
        info.isValid = false;
    } else {
        info.position = QPointF(x , y );  // 转为像素坐标
        info.radius = radius  + 20;  // 雷云更危险，安全距离增加到20px
        info.isValid = true;
//        qDebug() << "雷云实际半径:" << radius  << "，避障半径:" << info.radius;
    }
    return info;
}

// 全能战士策略：评估环境复杂度
float Strategy4::evaluateEnvironmentComplexity(const ObstacleInfo& mountain, const ObstacleInfo& radar,
                                              const ObstacleInfo& cloud, int enemyCount)
{
    float complexity = 0.0f;

    // 障碍物复杂度评估
    if (mountain.isValid) complexity += 0.3f;
    if (radar.isValid) complexity += 0.25f;
    if (cloud.isValid) complexity += 0.35f; // 雷云最危险

    // 敌机数量影响复杂度
    complexity += enemyCount * 0.15f;

    // 限制在0-1范围内
    return qMin(1.0f, complexity);
}

// 全能战士策略：根据环境复杂度自适应调整参数
void Strategy4::adaptParametersToEnvironment(const QString& uavId, float complexity)
{
    if (!avoidanceStates.contains(uavId)) return;

    AvoidanceState& state = avoidanceStates[uavId];

    // 根据环境复杂度调整行为模式切换阈值
    // 复杂环境下更保守，简单环境下更激进
    if (complexity > 0.7f) {
        // 高复杂度：更保守的策略
        state.mode = (state.mode == INTERCEPT) ? PURSUE : state.mode;
    } else if (complexity < 0.3f) {
        // 低复杂度：更激进的策略
        if (state.mode == PURSUE) {
            // 在简单环境下更容易切换到拦截模式
        }
    }

    // 记录环境复杂度用于调试
    // qDebug() << "全能战士" << uavId << "环境复杂度:" << complexity;
}
