#include "mainwindow.h"
#include "ui_mainwindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    this->setWindowTitle("自动策略飞行控制终端");
    // 初始化日志系统
    setupLogSystem();
    // 设置B1 B2 B3标签为蓝色
    ui->B1->setStyleSheet("color: blue; font-weight: bold;");
    ui->B2->setStyleSheet("color: blue; font-weight: bold;");
    ui->B3->setStyleSheet("color: blue; font-weight: bold;");

    // 设置R1 R2 R3标签为红色
    ui->R1->setStyleSheet("color: red; font-weight: bold;");
    ui->R2->setStyleSheet("color: red; font-weight: bold;");
    ui->R3->setStyleSheet("color: red; font-weight: bold;");
    // 设置标题字体大小
    QFont statusFont("Microsoft YaHei", 14, QFont::Bold);
    QFont strategyFont("Microsoft YaHei", 14, QFont::Bold);
    QFont recordFont("Microsoft YaHei", 14, QFont::Bold);
    QFont mapFont("Microsoft YaHei", 15, QFont::Bold);
    // 设置主标题字体
    ui->groupBox_3->setFont(statusFont);
    ui->groupBox_4->setFont(strategyFont);
    ui->groupBox_6->setFont(recordFont);
    ui->groupBox_5->setFont(mapFont);
    // 设置子标题字体
    QFont blueUAVFont("Microsoft YaHei", 11);
    QFont redUAVFont("Microsoft YaHei", 11);
    ui->groupBox->setFont(blueUAVFont);
    ui->groupBox_2->setFont(redUAVFont);
    // 设置游戏状态和剩余时间字体
    QFont timeStatusFont("Microsoft YaHei", 13, QFont::Bold);
    ui->time_status->setFont(timeStatusFont);

    // 创建自定义奖杯图标
    QPixmap trophyPixmap(ui->win_icon->size());
    trophyPixmap.fill(Qt::transparent);

    QPainter painter(&trophyPixmap);
    painter.setRenderHint(QPainter::Antialiasing);

    // 更深的金色
    QColor trophyColor(218, 165, 32);  // 深金色 #DAA520
    QColor highlightColor(255, 215, 0); // 高光金色
    QColor shadowColor(139, 105, 20);   // 阴影金色
    QColor baseColor(90, 90, 90);

    int w = trophyPixmap.width();
    int h = trophyPixmap.height();

    // 向上偏移量
    int yOffset = h * 0.30;

    // 杯身参数
    const int cupWidth = 36, cupHeight = 38;
    int cLeft = w/2 - cupWidth/2, cTop = h*0.18 - yOffset;
    int cBot = cTop + cupHeight;

    // 杯身路径
    QPainterPath cupPath;
    cupPath.moveTo(cLeft, cTop + 8);
    cupPath.arcTo(QRectF(cLeft, cTop, cupWidth, 16), 180, 180); // 上半椭圆
    cupPath.quadTo(w/2 + cupWidth*0.5, cBot - 10, w/2, cBot + 12);
    cupPath.quadTo(w/2 - cupWidth*0.5, cBot - 10, cLeft, cTop + 8);
    cupPath.closeSubpath();

    // 杯身主体（深金色）
    painter.setPen(Qt::NoPen);
    painter.setBrush(trophyColor);
    painter.drawPath(cupPath);

    // 添加3D效果 - 左侧高光
    QLinearGradient leftHighlight(cLeft, cTop, cLeft + cupWidth/3, cTop);
    leftHighlight.setColorAt(0, highlightColor);
    leftHighlight.setColorAt(1, Qt::transparent);
    painter.setBrush(QBrush(leftHighlight));
    painter.drawPath(cupPath);

    // 添加3D效果 - 右侧阴影
    QLinearGradient rightShadow(cLeft + cupWidth*2/3, cTop, cLeft + cupWidth, cTop);
    rightShadow.setColorAt(0, Qt::transparent);
    rightShadow.setColorAt(1, shadowColor);
    painter.setBrush(QBrush(rightShadow));
    painter.drawPath(cupPath);

    // 杯口完整椭圆高光边
    QRectF rimRect(w/2-cupWidth/2, cTop, cupWidth, 16);
    QPen rimPen(QColor(255,255,250,220), 2); // 明亮色，带透明度
    painter.setPen(rimPen);
    painter.setBrush(Qt::NoBrush);
    painter.drawEllipse(rimRect);

    // 杯口顶部渐变色 - 调整为更深的金色
    QRadialGradient cupTopGrad(w/2, cTop, cupWidth/2);
    // 更深的金色渐变
    cupTopGrad.setColorAt(0.0, QColor(250, 220, 120, 220)); // 中央高光更深
    cupTopGrad.setColorAt(0.5, QColor(230, 198, 80, 200));  // 中间区域更深
    cupTopGrad.setColorAt(1.0, QColor(210, 170, 60, 180));  // 边缘更深

    painter.setPen(Qt::NoPen);
    painter.setBrush(cupTopGrad);
    painter.drawEllipse(rimRect);
    // 杯颈 (整体上移)
    int neckW = 12, neckH = 11;
    int neckX = w/2-neckW/2, neckY = cBot + 10;
    // 杯颈渐变
    QLinearGradient neckGradient(neckX, neckY, neckX + neckW, neckY);
    neckGradient.setColorAt(0, shadowColor);
    neckGradient.setColorAt(0.5, trophyColor);
    neckGradient.setColorAt(1, shadowColor);
    painter.setBrush(QBrush(neckGradient));
    painter.drawRect(neckX, neckY, neckW, neckH);

    // 底座 (整体上移，宽度缩窄)
    painter.setBrush(baseColor);
    painter.setPen(Qt::NoPen);
    int baseY1 = neckY + neckH - 2;
    int baseY2 = baseY1 + 7;
    int baseW1 = 20, baseW2 = 32;
    painter.drawRect(w/2 - baseW1/2, baseY1, baseW1, 7);
    painter.drawRect(w/2 - baseW2/2, baseY2, baseW2, 6);

    // WIN字样
    QRect winRect(w/2-cupWidth/2+4, cTop+13, cupWidth-8, cupHeight-16);
    painter.setPen(Qt::white);
    QFont font("Arial", 8, QFont::Bold);
    painter.setFont(font);
    painter.drawText(winRect, Qt::AlignCenter, "WIN");
    painter.end();

    ui->win_icon->setPixmap(trophyPixmap);
    ui->win_icon->setText("");

    // 设置胜率文本字体
    QFont winRateFont("Arial", 14, QFont::Bold);
    ui->win_rate->setFont(winRateFont);
    ui->win_rate->setStyleSheet("color: #DAA520;"); // 金色文字


    // 初始化栅格地图
    gridMap = new GridMap(ui->gridmap);
    gridMap->setGeometry(0, 0, ui->gridmap->width(), ui->gridmap->height());
    gridMap->show();
    gridMap->initMap();

//    //连接栅格地图中计算而得的速度
//    connect(gridMap, &GridMap::velocityUpdated, this, [this](const QString& droneId, const QPointF& velocity) {
//        // 发送控制命令
//        sendControlCommand(droneId, velocity);
//    });

    // 创建并初始化 VsoaManager
    vsoaManager = new VsoaManager(this);

    // 连接 VsoaManager 的信号到 MainWindow 的槽
    connect(vsoaManager, &VsoaManager::connectionStatusChanged, this, &MainWindow::onConnectionStatusChanged);
    connect(vsoaManager, &VsoaManager::gameDataUpdated, this, &MainWindow::onGameDataUpdated);
    connect(vsoaManager, &VsoaManager::serverPaused, this, &MainWindow::onServerPaused);
    connect(vsoaManager, &VsoaManager::serverResumed, this, &MainWindow::onServerResumed);

    // 初始化 VSOA 连接
    vsoaManager->initConnection();

    // 初始化移动物体刷新定时器
    movingObjectsTimer = new QTimer(this);
    connect(movingObjectsTimer, &QTimer::timeout, this, &MainWindow::clearStaleMovingObjects);
    movingObjectsTimer->start(500);

    // 初始化地图刷新定时器
    QTimer *refreshTimer = new QTimer(this);
    connect(refreshTimer, &QTimer::timeout, [this]() {
        if (gridMap->needsUpdate()) {
            gridMap->update();
        }
    });
    refreshTimer->start(50);

    // 连接策略按钮信号
    connect(ui->SO1, &QPushButton::clicked, this, [this]() {
        if (gameStage != "running") {
            currentStrategy = 1;
            ui->SO1->setStyleSheet("background-color: #8FBC8F;");
            ui->SO2->setStyleSheet("");
            ui->SO3->setStyleSheet("");
            ui->SO4->setStyleSheet("");
            appendLog("已选择策略1 (优化的A*算法路径规划、A*结合BFS动态避障)");

            // 清理策略2MADDPG算法资源
            cleanupMADDPG();
            // 清理策略4人工势场算法资源
            cleanupStrategy4();

            // 初始化策略1和策略3PathPlanner A*算法资源
            initializePathPlanner();
        }
    });

    connect(ui->SO2, &QPushButton::clicked, this, [this]() {
        if (gameStage != "running") {
            currentStrategy = 2;
            ui->SO1->setStyleSheet("");
            ui->SO2->setStyleSheet("background-color: #8FBC8F;");
            ui->SO3->setStyleSheet("");
            ui->SO4->setStyleSheet("");
            appendLog("已选择策略2 (MADDPG)");

            // 清理策略1和策略3PathPlanner A*算法资源
            cleanupPathPlanner();
            // 清理策略4人工势场算法资源
            cleanupStrategy4();

            // 初始化策略2MADDPG算法资源
            initializeMADDPG();
        }
    });

    connect(ui->SO3, &QPushButton::clicked, this, [this]() {
        if (gameStage != "running") {
            currentStrategy = 3;
            ui->SO1->setStyleSheet("");
            ui->SO2->setStyleSheet("");
            ui->SO3->setStyleSheet("background-color: #8FBC8F;");
            ui->SO4->setStyleSheet("");
            appendLog("已选择策略3 (蛇形阵列策略)优化局部A*算法结合BFS避障");

            // 清理策略2MADDPG算法资源
            cleanupMADDPG();
            // 清理策略4人工势场算法资源
            cleanupStrategy4();

            // 初始化策略1和策略3PathPlanner A*算法资源
            initializePathPlanner();
        }
    });

    connect(ui->SO4, &QPushButton::clicked, this, [this]() {
        if (gameStage != "running") {
            currentStrategy = 4;
            ui->SO1->setStyleSheet("");
            ui->SO2->setStyleSheet("");
            ui->SO3->setStyleSheet("");
            ui->SO4->setStyleSheet("background-color: #8FBC8F;");
            appendLog("已选择策略4 (人工势场动态避障算法)");

            // 清理策略1和策略3PathPlanner A*算法资源
            cleanupPathPlanner();
            // 清理策略2MADDPG算法资源
            cleanupMADDPG();

            // 初始化策略4人工势场算法资源
            initializeStrategy4();
        }
    });

    // 默认选中策略1
    ui->SO1->setStyleSheet("background-color: #8FBC8F;");
    appendLog("程序启动，默认选择策略1 (优化的A*算法路径规划、A*结合BFS动态避障)");
    // 默认初始化PathPlanner
    initializePathPlanner();

    // 初始化SO1和TargetManager
    SO1 = new Strategy1(this);
    //连接SO1的日志输出到mainwindow窗口显示
    connect(SO1, &Strategy1::logMessage, this, &MainWindow::receiveLogMessage);
    SO3 = new Strategy3(this);  // ！初始化Strategy3对象，用于蛇形阵列策略
    // 连接Strategy3的信号
    connect(SO3, &Strategy3::needReplanPath, this, &MainWindow::onStrategy3NeedReplanPath);  // ！连接Strategy3的重规划路径信号
    targetManager = new TargetManager(this);
    //连接TargetManager的日志输出到mainwindow窗口显示
    connect(targetManager, &TargetManager::logMessage, this, &MainWindow::receiveLogMessage);

    // 初始化策略管理器
    strategyManager = new UAVStrategyManager(this);

    setCompetitionMode(gridMap->showMapInCompetitionMode);         //是否显示地图
}

MainWindow::~MainWindow()
{
    // 清理算法资源
    cleanupPathPlanner();
    cleanupMADDPG();
    cleanupStrategy4();
    delete targetManager;
    delete SO1;

    delete ui;
}

// 处理 VsoaManager 信号的槽函数
void MainWindow::onConnectionStatusChanged(bool connected, QString info)
{
    // 处理连接状态变化
    if (connected) {
        appendLog("已连接到服务器: " + info);
    } else {
        appendLog("服务器连接失败或断开: " + info);
    }
}

void MainWindow::onServerPaused(QDateTime pauseStartTime)        //服务器暂停游戏
{
    movingObjectsTimer->stop();
    serverPaused = true;
    serverPauseStartTime = pauseStartTime;
}

void MainWindow::onServerResumed(int pausedSeconds)        //暂停后继续，计算本次暂停时间
{
    movingObjectsTimer->start(500);
    serverPaused = false;
    totalPauseSeconds += pausedSeconds;
    qDebug() << "服务器恢复，本次暂停时间：" << pausedSeconds << "秒，自上次检测到红方无人机到现在总暂停时间：" << totalPauseSeconds << "秒";
}

void MainWindow::setCompetitionMode(bool isCompetition) {       //设置比赛模式是否显示地图
   if (isCompetition) {
       ui->groupBox_5->setVisible(true);
   } else {
       ui->groupBox_5->setVisible(false);
       ui->centralwidget->setFixedSize(642, 820);
   }
}

void MainWindow::sendControlCommand(const QString &uavId, const QPointF &velocity)
{
    // 使用 VsoaManager 发送控制命令
    vsoaManager->sendControlCommand(uavId, velocity);
}

void MainWindow::onGameDataUpdated(const QMap<QString, DroneInfo> &updatedDronesInfo,
                                  const QMap<QString, ObstacleInfo> &updatedObstaclesInfo,
                                  const QMap<QString, ObstacleInfo> &updatedStaticObstacles,
                                  const QMap<QString, ObstacleInfo> &updatedMovingObstacles,
                                  int updatedGameLeftTime,
                                  QString updatedGameStage)
{
    // 检查游戏状态是否变化
    bool gameStateChanged = (gameStage != updatedGameStage);

    // 更新本地数据
    dronesInfo = updatedDronesInfo;
    obstaclesInfo = updatedObstaclesInfo;
    staticObstacles = updatedStaticObstacles;
    movingObstacles = updatedMovingObstacles;
    gameLeftTime = updatedGameLeftTime;

    // 计算雷云速度
    calculateCloudVelocities();

    // 策略3蛇头变更检测
    static QString lastSnakeHead = "";
    if (currentStrategy == 3 && gameStage == "running") { // ！策略3主流程入口
        // 确定当前的蛇头
        QString newSnakeHead = "";
        if (dronesInfo.contains("B1") && dronesInfo["B1"].hp > 0) {
            newSnakeHead = "B1";  // B1优先作为蛇头
        } else if (dronesInfo.contains("B2") && dronesInfo["B2"].hp > 0) {
            newSnakeHead = "B2";  // B1坠毁时B2成为蛇头
        } else if (dronesInfo.contains("B3") && dronesInfo["B3"].hp > 0) {
            newSnakeHead = "B3";  // B1和B2坠毁时B3成为蛇头
        }

        // 检测蛇头是否变化（例如之前的蛇头坠毁）
        if (!lastSnakeHead.isEmpty() && lastSnakeHead != newSnakeHead) {
            appendLog("策略3：检测到蛇头变更："+lastSnakeHead+" -> "+newSnakeHead);

            // 如果新蛇头存在，为其重新规划路径
            if (!newSnakeHead.isEmpty() && SO3) { // ！调用SO3相关逻辑
                // 获取当前巡逻点或目标点
                QPoint targetPoint;
                if (!SO3->PATROL_POINTS.isEmpty()) {
                    // 使用策略3的第一个巡逻点作为初始目标
                    targetPoint = SO3->PATROL_POINTS[0];
                    // 转换为像素坐标
                    targetPoint = QPoint(
                        targetPoint.x() * gridMap->GRID_SIZE + gridMap->GRID_SIZE/2,
                        targetPoint.y() * gridMap->GRID_SIZE + gridMap->GRID_SIZE/2
                    );
//                    qDebug() << "[Strategy3] 新蛇头" << newSnakeHead << "规划路径到巡逻点:" << targetPoint;
                    appendLog(QString("策略3：新蛇头"+newSnakeHead+"规划路径到巡逻点:(%1,%2)").arg(targetPoint.x()).arg(targetPoint.y()));

                    // 规划路径
                    QPoint currentPos;
                    if (dronesInfo.contains(newSnakeHead)) {
                        int gridCol = dronesInfo[newSnakeHead].x / gridMap->GRID_SIZE;
                        int gridRow = dronesInfo[newSnakeHead].y / gridMap->GRID_SIZE;
                        currentPos = QPoint(gridCol, gridRow);
                    }
                    planPathForSingleDrone_S3(newSnakeHead, currentPos, targetPoint); // ！调用策略3路径规划
                }
            }
        }

        // 更新上一个蛇头记录
        lastSnakeHead = newSnakeHead;
    } else if (currentStrategy != 3 || gameStage != "running") {
        // 如果不是策略3或不在运行状态，重置蛇头记录
        lastSnakeHead = "";
    }

    // 检查游戏状态是否变为running
    if (gameStateChanged && updatedGameStage == "running" && gameStage != "running") {
        m_lastPathPlanTime.clear();
//        pathPlanner->resetEscapeState();
        // 重新初始化巡逻目标点
        if(currentStrategy == 1){
        targetManager->initPresetTargets();
        // 可以添加一个短暂延迟，等待一段时间后再规划路径
        QTimer::singleShot(10, this, [this]() {
            planPathToPresetTargets();
        });
        }else if(currentStrategy == 3){
            targetManager->initPresetTargets();
            SO3->reset(); // ！策略3重置
            // 给策略3一些时间初始化
            QTimer::singleShot(100, this, [this]() { // ！策略3初始化流程
                // 更新战场态势
                QMap<QString, DroneState> friendlyStates;
                QMap<QString, DroneState> enemyStates;
                for (const auto& drone : dronesInfo.keys()) {
                    if (drone.startsWith("B")) {
                        friendlyStates[drone] = {QPoint(dronesInfo[drone].x, dronesInfo[drone].y), dronesInfo[drone].hp};
                    } else if (drone.startsWith("R")) {
                        enemyStates[drone] = {QPoint(dronesInfo[drone].x, dronesInfo[drone].y), dronesInfo[drone].hp};
                    }
                }

                // 先更新战场态势，让策略3知道敌机位置
                SO3->updateGameState(friendlyStates, enemyStates); // ！更新策略3战场态势

                // 在蛇形模式下，只为当前蛇头规划路径，其他无人机直接跟随
                // 确定当前的蛇头
                QString snakeHead = "";
                if (dronesInfo.contains("B1") && dronesInfo["B1"].hp > 0) {
                    snakeHead = "B1";  // B1优先作为蛇头
                } else if (dronesInfo.contains("B2") && dronesInfo["B2"].hp > 0) {
                    snakeHead = "B2";  // B1坠毁时B2成为蛇头
                } else if (dronesInfo.contains("B3") && dronesInfo["B3"].hp > 0) {
                    snakeHead = "B3";  // B1和B2坠毁时B3成为蛇头
                }

                if (!snakeHead.isEmpty()) {
                    planPathForSingleDrone_S3(snakeHead); // ！只为蛇头规划路径
                    appendLog("策略3：游戏开始，只为蛇头"+snakeHead+"规划路径，B2和B3将直接跟随");
                }
            });
        }
    }

    // 检查游戏状态是否变为finish
    if (updatedGameStage == "finish" && gameStage != "finish") {
        staclePositions.clear();
        m_lastPathPlanTime.clear();
        resetRedDroneUpdateLabels = true;
        // 清空无人机位置
        gridMap->clearDronePositions();
        // 清空所有路径
        gridMap->clearPath();
        gridMap->initMap();
        // 清空静态障碍物标记容器
        staticObstacleMarkers.clear();
        if(currentStrategy == 1||currentStrategy == 3){
            // 重置所有无人机的目标点到达状态
            QStringList droneIds = {"B1", "B2", "B3"};
            for (const QString& droneId : droneIds) {
                targetManager->setTargetReached(droneId, false);
            }
        }
        if(currentStrategy == 2){
            // 初始化lastObservation
            QStringList blueUAVs = {"B1", "B2", "B3"};
            int dim = 31; // 维度为31
            for (const QString& uavId : blueUAVs) {
                lastObservation[uavId] = QVector<float>(dim, -1.0f);  // 用31个-1.0f初始化
            }
        }
    }
    gameStage = updatedGameStage;

    // 如果游戏状态发生变化，更新策略按钮状态
    if (gameStateChanged) {
        updateStrategyButtonsState();
    }

    // 更新栅格地图
    // 清空无人机位置
    gridMap->clearDronePositions();

    // 更新无人机位置
    for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
        const QString &uid = it.key();
        const DroneInfo &info = it.value();

        if (uid.startsWith("B")) {
            if (info.hp > 0) {
                // 蓝方无人机（己方）位置更新
                gridMap->addself(uid, info.x, info.y, info.hp);
            } else {
                // 蓝方无人机死亡，清空其路径
                gridMap->clearPath(uid);
            }
        } else if (uid.startsWith("R") && info.hp > 0) {
            // 红方无人机（敌方）位置更新
            // 检查是否在任何蓝方无人机的探测范围内
            bool inDetectionRange = false;
            for (auto blueIt = dronesInfo.begin(); blueIt != dronesInfo.end(); ++blueIt) {
                if (blueIt.key().startsWith("B") && blueIt.value().hp > 0) {
                    // 计算距离
                    double dx = info.x - blueIt.value().x;
                    double dy = info.y - blueIt.value().y;
                    double distance = sqrt(dx*dx + dy*dy);

                    if (distance <= 300) { // 探测范围为300
                        inDetectionRange = true;
                        gridMap->addEnemy(uid, info.x, info.y, info.hp);
                        break;
                    }
                }
            }
        }
    }

    // 更新障碍物
    // [1] 先处理静态障碍物（山体、雷达）
    for (auto it = staticObstacles.begin(); it != staticObstacles.end(); ++it) {
        const ObstacleInfo &info = it.value();
        QString obstacleId = info.id;

        // 检查该静态障碍物是否已在标记容器中
        if (!staticObstacleMarkers.contains(obstacleId)) {
            // 如果不存在，添加到标记容器中，并设置hasAdded为false
            StaticObstacleMarker marker;
            marker.id = obstacleId;
            marker.hasAdded = false;
            staticObstacleMarkers.insert(obstacleId, marker);

        }

        // 获取该障碍物的标记
        StaticObstacleMarker &marker = staticObstacleMarkers[obstacleId];

        // 如果该静态障碍物尚未添加到地图中，则添加
        if (!marker.hasAdded) {
            // 更新地图
            emit UpdateSharedGridMap(gridMap->getSharedGridMap());
            // 存储静态障碍物位置信息，用于判断重规划
            staclePositions[info.id] = {info.x, info.y, info.r};
            if (info.type == "mountain") {
                gridMap->addObstacle(info.id, info.x, info.y, info.r, MOUNTAIN);
                marker.hasAdded = true;
            } else if (info.type == "radar") {
                gridMap->addObstacle(info.id, info.x, info.y, info.r, RADAR);
                marker.hasAdded = true;
            }
        }
    }
    // [2] 再一次性刷新移动障碍（如云）
    for (auto it = obstaclesInfo.begin(); it != obstaclesInfo.end(); ++it) {
        const ObstacleInfo &info = it.value();
        if (info.type == "cloud") {
            if(currentStrategy == 1||currentStrategy == 3){
                gridMap->addObstacle(info.id, info.x, info.y, gridMap->CLOUD_RADIUS, CLOUD);
            }else{
                gridMap->addObstacle(info.id, info.x, info.y, 80, CLOUD);
            }

            // 存储雷云位置信息
            staclePositions[info.id] = {info.x, info.y, 100};
        }
    }

    // 遍历蓝方无人机，检查路径是否在障碍物范围内S01 SO3使用重新规划
    if(currentStrategy == 1 || currentStrategy == 3){
        // 策略3使用更长的重规划间隔，减少因障碍物导致的频繁重规划
        int PATH_PLAN_INTERVAL = currentStrategy == 3 ? 300 : 150;  // ！策略3重规划间隔
        for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
            const QString &uid = it.key();

            // 只处理蓝方无人机
            if (!uid.startsWith("B") || it.value().hp <= 0) {
                continue;
            }

            if(currentStrategy == 1){
                // 如果有平滑路径且不为空
                if (gridMap->m_pathMap.contains(uid) && !gridMap->m_pathMap[uid].isEmpty() &&
                    gridMap->m_smoothedPathMap.contains(uid) && gridMap->m_smoothedPathMap[uid].size() > 1) {

                    // 检查该无人机的路径是否在障碍物范围内
                    if (isPathInObstacle(uid)) {
                        // 检查是否已经过了规划间隔时间
                        QTime currentTime = QTime::currentTime();
                        if (!m_lastPathPlanTime.contains(uid) ||
                            m_lastPathPlanTime[uid].msecsTo(currentTime) >= PATH_PLAN_INTERVAL) {

                            // 更新上次规划时间
                            m_lastPathPlanTime[uid] = currentTime;

                            // 只为这个无人机重规划
                            planPathForSingleDrone_S1(uid);
                            appendLog("策略1：路径在障碍物中，无人机 "+uid+" 需要重规划路径");
                        }
                        // 不使用break，继续检查其他无人机
                    }
                }
                else if(gridMap->m_pathMap[uid].isEmpty()) {
                    QPointF velocity(0,0);
                    sendControlCommand(uid, velocity);
                }
            }else{// 策略3只为当前蛇头检查路径，策略1为所有无人机检查
                // 确定当前的蛇头
                QString snakeHead = "";
                if (dronesInfo.contains("B1") && dronesInfo["B1"].hp > 0) {
                    snakeHead = "B1";  // B1优先作为蛇头
                } else if (dronesInfo.contains("B2") && dronesInfo["B2"].hp > 0) {
                    snakeHead = "B2";  // B1坠毁时B2成为蛇头
                } else if (dronesInfo.contains("B3") && dronesInfo["B3"].hp > 0) {
                    snakeHead = "B3";  // B1和B2坠毁时B3成为蛇头
                }

                // 只为蛇头检查路径
                if (uid != snakeHead) {
                    continue;
                }

                // 如果有平滑路径且不为空
                if (gridMap->m_pathMap.contains(snakeHead) && !gridMap->m_pathMap[snakeHead].isEmpty() &&
                    gridMap->m_smoothedPathMap.contains(snakeHead) && gridMap->m_smoothedPathMap[snakeHead].size() > 1) {

                    // 检查该无人机的路径是否在障碍物范围内
                    if (isPathInObstacle(snakeHead)) {
                        // 检查是否已经过了规划间隔时间
                        QTime currentTime = QTime::currentTime();
                        if (!m_lastPathPlanTime.contains(snakeHead) ||
                            m_lastPathPlanTime[snakeHead].msecsTo(currentTime) >= PATH_PLAN_INTERVAL) {
                            // 更新上次规划时间
                            m_lastPathPlanTime[snakeHead] = currentTime;
                            // 在策略3中，只为B1重规划路径
                            if (snakeHead == "B1") {
                                planPathForSingleDrone_S3(snakeHead); // ！策略3障碍物重规划
                                appendLog("策略3：路径在障碍物中，无人机蛇头： "+snakeHead+" 需要重规划路径");
                            }

                        }
                        // 不使用break，继续检查其他无人机
                    }
                }
                else if(gridMap->m_pathMap[uid].isEmpty()) {
                    QPointF velocity(0,0);
                    sendControlCommand(uid, velocity);
                }
            }
        }
    }

    // 更新UI显示
    updateUIDisplay();

    // 根据当前策略执行相应算法
    if (gameStage == "running") {
        if ((currentStrategy == 1||currentStrategy == 3) && isPathPlannerInitialized) {

            // 策略3蛇形阵列的蛇头确定
            QString snakeHead = "";
            if (currentStrategy == 3) {
                // 确定当前的蛇头：B1优先，如果B1坠毁则B2成为蛇头，如果B2也坠毁则B3成为蛇头
                if (dronesInfo.contains("B1") && dronesInfo["B1"].hp > 0) {
                    snakeHead = "B1";  // B1优先作为蛇头
                } else if (dronesInfo.contains("B2") && dronesInfo["B2"].hp > 0) {
                    snakeHead = "B2";  // B1坠毁时B2成为蛇头
                } else if (dronesInfo.contains("B3") && dronesInfo["B3"].hp > 0) {
                    snakeHead = "B3";  // B1和B2坠毁时B3成为蛇头
                }

                if (!snakeHead.isEmpty()) {
                }
            }

            //            // 策略1和策略3: A*路径规划
            //            // 获取蓝方无人机的信息
            //            for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
            //                const QString &uid = it.key();

            //                // 只处理蓝方无人机
            //                if (!uid.startsWith("B") || it.value().hp <= 0) {
            //                    continue;
            //                }

            //                // 如果有平滑路径且不为空
            //                if (gridMap->m_pathMap.contains(uid) && !gridMap->m_pathMap[uid].isEmpty() &&
            //                    gridMap->m_smoothedPathMap.contains(uid) && gridMap->m_smoothedPathMap[uid].size() > 1) {
            //                    // 获取无人机当前位置
            //                    QPointF currentPos(it.value().x, it.value().y);

            //                    // 使用GridMap计算速度
            //                        QPointF velocity = gridMap->calculateVelocity(uid, currentPos);
            //                        // 发送控制命令
            //                        sendControlCommand(uid, velocity);
            //                }  else if(gridMap->m_pathMap[uid].isEmpty()){     //路径为空时飞机暂停
            //                    QPointF velocity(0,0);
            //                    sendControlCommand(uid, velocity);
            //                }
            //            }

            for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
                const QString &uid = it.key();

                // 只处理蓝方无人机
                if (!uid.startsWith("B") || it.value().hp <= 0) {
                    continue;
                }
                // 策略1的移动逻辑
                if (currentStrategy == 1) {
                    // 为每个无人机计算速度并移动
                    if (gridMap->m_smoothedPathMap.contains(uid) && gridMap->m_smoothedPathMap[uid].size() > 1) {  // 如果有平滑路径且不为空
                        // 获取无人机当前位置
                        QPointF currentPos(it.value().x, it.value().y);

                        // 使用GridMap计算速度
                        QPointF velocity = gridMap->calculateVelocity(uid, currentPos);

                        // 发送计算出的速度指令
                        sendControlCommand(uid, velocity);
                    } else {     //路径无效或太短时飞机暂停
                        QPointF velocity(0,0);
                        sendControlCommand(uid, velocity);
                    }
                }

                // 策略3中，蛇头使用路径规划，其他无人机直接跟随
                 else if (currentStrategy == 3) {
                    // 如果是当前的蛇头，使用正常的路径规划逻辑
                    if (uid == snakeHead) {
                        if (gridMap->m_smoothedPathMap.contains(uid) && gridMap->m_smoothedPathMap[uid].size() > 1) {
                            // 获取无人机当前位置
                            QPointF currentPos(it.value().x, it.value().y);

                            // 使用GridMap计算速度
                            QPointF velocity = gridMap->calculateVelocity(uid, currentPos);

                            // 遵从指示，添加Debug打印
//                            qDebug() << "[VelocityCalc]" << uid << "Calculated Velocity:" << velocity;

                            // 发送计算出的速度指令
                            sendControlCommand(uid, velocity);
                        } else {     //路径无效或太短时飞机暂停
                            QPointF velocity(0,0);
                            sendControlCommand(uid, velocity);
                        }
                    } else if (uid == "B2" && currentStrategy == 3) {
                        // B2的逻辑：如果B1存活，则跟随B1；如果B1坠毁，则自己成为蛇头（已由前面的snakeHead判断处理）
                        // 确保B2不是蛇头
                        if (uid != snakeHead) {
                            // 确定路径源 - B2只跟随B1，如果B1坠毁，B2就成为蛇头
                            QString pathSource = "";
                            if (dronesInfo.contains("B1") && dronesInfo["B1"].hp > 0) {
                                pathSource = "B1";  // B1存活，跟随B1
                            } else {
                                // B1坠毁，B2应该已经成为蛇头，不应该进入这个分支
                                qDebug() << "[Strategy3] B2检测到B1已坠毁，但B2不是蛇头，逻辑错误";
                                continue;
                            }

                            // B2跟随B1的路径点
                            if (gridMap->m_smoothedPathMap.contains(pathSource) && gridMap->m_smoothedPathMap[pathSource].size() > 1) {
                                QPointF currentPos(it.value().x, it.value().y);

                                // 找到B1当前在路径上的位置索引
                                int headIndex = gridMap->findClosestPathPointIndex(
                                    QPointF(dronesInfo[pathSource].x, dronesInfo[pathSource].y),
                                    pathSource);

                                // B2跟随B1路径，但落后80个路径点
                                int b2TargetIndex = qMax(0, headIndex - 80);

                                // 获取目标点
                                QPointF targetPoint = gridMap->m_smoothedPathMap[pathSource][b2TargetIndex];

                                // 检查目标点到当前位置的路径是否有障碍物
                                bool hasObstacle = false;
                                QPoint currentGridPos(currentPos.x() / gridMap->GRID_SIZE, currentPos.y() / gridMap->GRID_SIZE);
                                QPoint targetGridPos(targetPoint.x() / gridMap->GRID_SIZE, targetPoint.y() / gridMap->GRID_SIZE);

                                // 检查从当前位置到目标点的直线路径上是否有障碍物
                                QList<QPoint> pathToCheck = getLinePoints(currentGridPos, targetGridPos);
                                for (const QPoint& point : pathToCheck) {
                                    if (isGridPointInObstacle(point)) {
                                        hasObstacle = true;
//                                        qDebug() << "[Strategy3] B2检测到前方路径有障碍物，需要避障";
                                        appendLog("策略3：B2检测到前方路径有障碍物，需要避障");
                                        break;
                                    }
                                }

                                if (hasObstacle) {
                                    // 如果检测到障碍物，寻找安全点并规划避障路径
                                    QPoint safePoint = findSafePointNearTarget(currentGridPos, targetGridPos);

                                    // 如果找到了安全点且与当前位置不同，规划路径到安全点
                                    if (safePoint != currentGridPos) {
                                        // 清除旧路径
                                        gridMap->clearPath(uid);

                                        // 规划新路径到安全点
                                        emit StartfindPath(currentGridPos, safePoint, uid);
//                                        qDebug() << "[Strategy3] B2规划避障路径到安全点:" << safePoint;
                                        appendLog(QString("策略3：B2规划避障路径到安全点:(%1,%2)").arg(safePoint.x()).arg(safePoint.y()));

                                        // 添加一个连接，当路径规划完成后立即执行移动
                                        QMetaObject::Connection* conn = new QMetaObject::Connection();
                                        *conn = connect(pathPlanner, &PathPlanner::pathPlanned, this, [this, uid, conn](const QVector<QPoint>& path, const QString& droneId) {
                                            // 确保这是我们正在等待的无人机的路径
                                            if (droneId == uid) {
                                                // 断开连接，避免重复处理
                                                disconnect(*conn);
                                                delete conn;

                                                // 检查路径是否有效
                                                if (!path.isEmpty() && gridMap->m_smoothedPathMap.contains(droneId) && gridMap->m_smoothedPathMap[droneId].size() > 1) {
                                                    // 获取无人机当前位置
                                                    QPointF currentPos(dronesInfo[droneId].x, dronesInfo[droneId].y);

                                                    // 使用GridMap计算速度
                                                    QPointF velocity = gridMap->calculateVelocity(droneId, currentPos);

//                                                    qDebug() << "[Strategy3] B2避障后计算速度:" << velocity;

                                                    // 发送控制命令
                                                    sendControlCommand(droneId, velocity);
                                                } else {
//                                                    qDebug() << "[Strategy3] B2避障路径规划失败或路径无效，保持静止";
                                                    QPointF velocity(0, 0);
                                                    sendControlCommand(droneId, velocity);
                                                }
                                            }
                                        });

                                        // 跳过后续处理，等待新路径规划完成
                                        continue;
                                    }
                                }

                                // 如果没有障碍物或无法找到安全点，继续正常跟随逻辑
                                // 计算方向向量
                                QPointF dirVector = targetPoint - currentPos;
                                float distance = qSqrt(dirVector.x() * dirVector.x() + dirVector.y() * dirVector.y());

                                // 初始化速度向量
                                QPointF velocity;

                                // 如果已经非常接近目标点，则匹配B1的速度以保持队形
                                if (distance < 5.0) {
                                    velocity = QPointF(dronesInfo["B1"].vx, dronesInfo["B1"].vy);
                                } else {
                                    // 否则，向目标点移动
                                    float maxSpeed = 50.0;
                                    if (distance > 0) {
                                        dirVector = QPointF(dirVector.x() / distance, dirVector.y() / distance);
                                        velocity = dirVector * maxSpeed;
                                    }
                                }

//                                qDebug() << "[FollowCalc] " << uid << " 跟随B1路径点，索引:" << b2TargetIndex
//                                        << "目标点:" << targetPoint << "速度:" << velocity;

                                // 发送控制命令
                                sendControlCommand(uid, velocity);
                                continue; // 跳过后续处理
                            }
                        }
                    } else if (uid == "B3" && currentStrategy == 3) {
                        // B3的逻辑：如果B2存活，则跟随B2；如果B2坠毁但B1存活，则跟随B1；如果B1和B2都坠毁，则自己成为蛇头（已由前面的snakeHead判断处理）
                        // 确定B3应该跟随谁
                        QString followTarget = "";
                        if (dronesInfo.contains("B2") && dronesInfo["B2"].hp > 0) {
                            followTarget = "B2";  // B2存活，跟随B2
                        } else if (dronesInfo.contains("B1") && dronesInfo["B1"].hp > 0) {
                            followTarget = "B1";  // B2坠毁但B1存活，跟随B1
                        }

                        // 确保B3不是蛇头且有跟随目标
                        if (uid != snakeHead && !followTarget.isEmpty()) {
                            // 确定要跟随的路径
                            QString pathSource = "";

                            // 确定当前的蛇头（路径源）
                            if (dronesInfo.contains("B1") && dronesInfo["B1"].hp > 0) {
                                pathSource = "B1";  // B1存活，使用B1的路径
                            } else if (dronesInfo.contains("B2") && dronesInfo["B2"].hp > 0) {
                                pathSource = "B2";  // B1坠毁但B2存活，使用B2的路径
                            } else {
                                qDebug() << "[Strategy3] B3无法确定路径源，无法跟随";
                                QPointF velocity(0, 0);
                                sendControlCommand(uid, velocity);
                                continue;
                            }

                            // 确保路径存在
                            if (gridMap->m_smoothedPathMap.contains(pathSource) && gridMap->m_smoothedPathMap[pathSource].size() > 1) {
                                QPointF currentPos(it.value().x, it.value().y);

                                // 找到路径源在路径上的位置索引
                                int sourceIndex = gridMap->findClosestPathPointIndex(
                                    QPointF(dronesInfo[pathSource].x, dronesInfo[pathSource].y),
                                    pathSource);

                                // 确定B3的目标索引
                                int b3TargetIndex;
                                if (followTarget == "B2") {
                                    // 如果跟随B2，则落后B2 80个点
                                    b3TargetIndex = qMax(0, sourceIndex - 160);
                                } else {
                                    // 如果直接跟随B1，则落后80个点
                                    b3TargetIndex = qMax(0, sourceIndex - 80);
                                }

                                // 获取目标点
                                QPointF targetPoint = gridMap->m_smoothedPathMap[pathSource][b3TargetIndex];

                                // 检查目标点到当前位置的路径是否有障碍物
                                bool hasObstacle = false;
                                QPoint currentGridPos(currentPos.x() / gridMap->GRID_SIZE, currentPos.y() / gridMap->GRID_SIZE);
                                QPoint targetGridPos(targetPoint.x() / gridMap->GRID_SIZE, targetPoint.y() / gridMap->GRID_SIZE);

                                // 检查从当前位置到目标点的直线路径上是否有障碍物
                                QList<QPoint> pathToCheck = getLinePoints(currentGridPos, targetGridPos);
                                for (const QPoint& point : pathToCheck) {
                                    if (isGridPointInObstacle(point)) {
                                        hasObstacle = true;
//                                        qDebug() << "[Strategy3] B3检测到前方路径有障碍物，需要避障";
                                        break;
                                    }
                                }

                                if (hasObstacle) {
                                    // 如果检测到障碍物，寻找安全点并规划避障路径
                                    QPoint safePoint = findSafePointNearTarget(currentGridPos, targetGridPos);

                                    // 如果找到了安全点且与当前位置不同，规划路径到安全点
                                    if (safePoint != currentGridPos) {
                                        // 清除旧路径
                                        gridMap->clearPath(uid);

                                        // 规划新路径到安全点
                                        emit StartfindPath(currentGridPos, safePoint, uid);
//                                        qDebug() << "[Strategy3] B3规划避障路径到安全点:" << safePoint;

                                        // 添加一个连接，当路径规划完成后立即执行移动
                                        QMetaObject::Connection* conn = new QMetaObject::Connection();
                                        *conn = connect(pathPlanner, &PathPlanner::pathPlanned, this, [this, uid, conn](const QVector<QPoint>& path, const QString& droneId) {
                                            // 确保这是我们正在等待的无人机的路径
                                            if (droneId == uid) {
                                                // 断开连接，避免重复处理
                                                disconnect(*conn);
                                                delete conn;

                                                // 检查路径是否有效
                                                if (!path.isEmpty() && gridMap->m_smoothedPathMap.contains(droneId) && gridMap->m_smoothedPathMap[droneId].size() > 1) {
                                                    // 获取无人机当前位置
                                                    QPointF currentPos(dronesInfo[droneId].x, dronesInfo[droneId].y);

                                                    // 使用GridMap计算速度
                                                    QPointF velocity = gridMap->calculateVelocity(droneId, currentPos);

//                                                    qDebug() << "[Strategy3] B3避障后计算速度:" << velocity;

                                                    // 发送控制命令
                                                    sendControlCommand(droneId, velocity);
                                                } else {
//                                                    qDebug() << "[Strategy3] B3避障路径规划失败或路径无效，保持静止";
                                                    QPointF velocity(0, 0);
                                                    sendControlCommand(droneId, velocity);
                                                }
                                            }
                                        });

                                        // 跳过后续处理，等待新路径规划完成
                                        continue;
                                    }
                                }

                                // 如果没有障碍物或无法找到安全点，继续正常跟随逻辑
                                // 计算方向向量
                                QPointF dirVector = targetPoint - currentPos;
                                float distance = qSqrt(dirVector.x() * dirVector.x() + dirVector.y() * dirVector.y());

                                // 初始化速度向量
                                QPointF velocity;

                                // 如果已经非常接近目标点，则匹配跟随目标的速度以保持队形
                                if (distance < 5.0) {
                                    velocity = QPointF(dronesInfo[followTarget].vx, dronesInfo[followTarget].vy);
                                } else {
                                    // 否则，向目标点移动
                                    float maxSpeed = 50.0;
                                    if (distance > 0) {
                                        dirVector = QPointF(dirVector.x() / distance, dirVector.y() / distance);
                                        velocity = dirVector * maxSpeed;
                                    }
                                }

//                                qDebug() << "[FollowCalc] " << uid << " 跟随" << followTarget << "，路径来源:" << pathSource
//                                        << "，索引:" << b3TargetIndex << "，目标点:" << targetPoint << "，速度:" << velocity;

                                // 发送控制命令
                                sendControlCommand(uid, velocity);
                                continue; // 跳过后续处理
                            } else {
//                                qDebug() << "[Strategy3] B3无法找到" << pathSource << "的路径，无法跟随";
                                // 如果找不到路径，尝试原地悬停
                                QPointF velocity(0, 0);
                                sendControlCommand(uid, velocity);
                                continue;
                            }
                        } else if (uid == snakeHead) {
                            // B3已成为蛇头，由前面的snakeHead逻辑处理
//                            qDebug() << "[Strategy3] B3已成为蛇头，使用蛇头逻辑";
                            appendLog("策略3： B3已成为蛇头，使用蛇头逻辑");
                        } else {
                            // 没有可跟随的目标，尝试原地悬停
//                            qDebug() << "[Strategy3] B3没有可跟随的目标，原地悬停";
                            QPointF velocity(0, 0);
                            sendControlCommand(uid, velocity);
                            continue;
                        }
                    }
                }
            }

        } else if (currentStrategy == 2 && isMaddpgInitialized) {
            // 策略2: MADDPG算法
            // 静态变量，用于记录每个无人机的折射状态和持续时间
            static QMap<QString, int> reflectionCounter;
            static QMap<QString, bool> reflectX;
            static QMap<QString, bool> reflectY;

            for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
                const QString &uid = it.key();
                const DroneInfo &info = it.value();

                // 只处理蓝方无人机且HP大于0的
                if (uid.startsWith("B") && info.hp > 0) {
                    // 初始化计数器（如果不存在）
                    if (!reflectionCounter.contains(uid)) {
                        reflectionCounter[uid] = 0;
                        reflectX[uid] = false;
                        reflectY[uid] = false;
                    }

                    QTime ctime =QTime::currentTime();
                    if (!m_lastinfer.contains(uid) || m_lastinfer[uid].msecsTo(ctime) >= INFER_INTERVAL){
                        m_lastinfer[uid] = ctime;
                        // 构建观察向量
                        observation = buildObservationForUAV(uid);

                        if (!observation.isEmpty() && Strategy2s.contains(uid)) {
                            // 使用UAVModel进行推理
                            QPointF velocity;
                            if(needInference){
                                velocity = Strategy2s[uid]->inference(observation);
                            }else{
                                velocity = QPointF(observation[2]*50,observation[3]*50);

                                // 边界检测：当无人机到达地图边界时实现镜面折射效果
                                // 地图边界：x=0或1280，y=0或800

                                // 检查并处理边界折射
                                // 左边界 (x=0)
                                if (info.x == 0) {
                                    velocity.setX(50); // 水平方向镜面反射
                                }
                                // 右边界 (x=1280)
                                else if (info.x == 1280) {
                                    velocity.setX(-50); // 水平方向镜面反射
                                }

                                // 上边界 (y=0)
                                if (info.y == 0) {
                                    velocity.setY(50); // 垂直方向镜面反射
                                }
                                // 下边界 (y=800)
                                else if (info.y == 800) {
                                    velocity.setY(-50); // 垂直方向镜面反射
                                }
                            }

                            // 发送控制命令
                            sendControlCommand(uid, velocity);
                        }
                    }
                }
            }
        }else if (currentStrategy == 4 && isStrategy4Initialized) {
            // 策略4: 人工势场算法
            for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
                const QString &uid = it.key();
                const DroneInfo &info = it.value();

                // 只处理蓝方无人机且HP大于0的
                if (uid.startsWith("B") && info.hp > 0) {
                    // 构建包含障碍物信息的27维观察向量
                    QVector<float> observation = buildS04Vector();

                    // 使用人工势场算法计算速度
                    QPointF velocity = SO4->calculateVelocity(uid, observation);

                    QTime currentTime = QTime::currentTime();
                    if (logouput.msecsTo(currentTime) >= 0) {
                        logouput = currentTime;
                        QString logMess = SO4->log;
                        appendLog(logMess);
                    }

                    // 发送控制命令
                    sendControlCommand(uid, velocity);
                }
            }
        }
    }
}

// 更新策略按钮状态
void MainWindow::updateStrategyButtonsState() {
    // 如果游戏正在运行，禁用所有策略按钮
    bool enabled = (gameStage != "running");

    ui->SO1->setEnabled(enabled);
    ui->SO2->setEnabled(enabled);
    ui->SO3->setEnabled(enabled);
    ui->SO4->setEnabled(enabled);
}

void MainWindow::clearStaleMovingObjects() {        // 清除500ms秒未更新的移动障碍物
    if (gridMap) {
        gridMap->clearStaleObstacles(500);
    }
}

void MainWindow::updateUIDisplay()           //更新自动飞行终端ui界面显示
{
    // 更新游戏状态和时间显示，使用HTML格式增强显示效果
    QString gameStatusText;
    if (gameStage == "running") {
        gameStatusText = "<span style='color:green;'>运行中</span>";
    } else if (gameStage == "pause") {
        gameStatusText = "<span style='color:orange;'>已暂停</span>";
    } else if (gameStage == "finish") {
        gameStatusText = "<span style='color:red;'>已结束</span>";
    } else {
        gameStatusText = "<span style='color:blue;'>初始化</span>";
    }

    ui->time_status->setText(QString("游戏状态: %1         剩余时间: <span style='color:blue; font-weight:bold;'>%2</span>秒")
                            .arg(gameStatusText)
                            .arg(gameLeftTime));
    ui->time_status->setTextFormat(Qt::RichText);
    // 更新蓝方无人机信息
    QMap<QString, QProgressBar*> blueHpBars = {
        {"B1", ui->Bhp1},
        {"B2", ui->Bhp2},
        {"B3", ui->Bhp3}
    };

    QMap<QString, QLabel*> bluePoints = {
        {"B1", ui->Bponit1},
        {"B2", ui->Bponit2},
        {"B3", ui->Bponit3}
    };

    QMap<QString, QLabel*> blueVelocities = {
        {"B1", ui->Bv1},
        {"B2", ui->Bv1_2},
        {"B3", ui->Bv1_3}
    };

    QMap<QString, QLabel*> blueStatus = {
        {"B1", ui->Bstat1},
        {"B2", ui->Bstat2},
        {"B3", ui->Bstat3}
    };

    // 更新红方无人机信息
    QMap<QString, QProgressBar*> redHpBars = {
        {"R1", ui->Rhp1},
        {"R2", ui->Rhp2},
        {"R3", ui->Rhp3}
    };

    QMap<QString, QLabel*> redPoints = {
        {"R1", ui->Rponit1},
        {"R2", ui->Rponit2},
        {"R3", ui->Rponit3}
    };

//    QMap<QString, QLabel*> redVelocities = {
//        {"R1", ui->Bv1_6},
//        {"R2", ui->Bv1_5},
//        {"R3", ui->Bv1_4}
//    };

    QMap<QString, QLabel*> redStatus = {
        {"R1", ui->Rstat1},
        {"R2", ui->Rstat2},
        {"R3", ui->Rstat3}
    };

    QMap<QString, QLabel*> redUpdateTimes = {
        {"R1", ui->updataetime1},
        {"R2", ui->updataetime2},
        {"R3", ui->updataetime3}
    };

    // 当前时间，用于计算红方无人机信息更新时间
    QDateTime currentTime = QDateTime::currentDateTime();

    // 静态变量，用于记录红方无人机上次被探测到的时间
    static QMap<QString, QDateTime> lastRedDroneDetectionTime;
    // 静态变量，用于记录红方无人机是否曾经被探测到
    static QMap<QString, bool> redDroneEverDetected;
    // 静态变量，用于记录红方无人机最后一次在视野中的时间
    static QMap<QString, QDateTime> lastRedDroneInSightTime;

    // 如果游戏状态变为finish，重置红方无人机状态
    if (resetRedDroneUpdateLabels) {
        // 重置所有红方无人机的状态
        for (auto it = redUpdateTimes.begin(); it != redUpdateTimes.end(); ++it) {
            QString uid = it.key();
            totalPauseSeconds = 0;
            // 重置更新时间标签为"未更新"
            redUpdateTimes[uid]->setText("未更新");
            // 重置血量为0
            redHpBars[uid]->setValue(0);
            // 重置坐标
            redPoints[uid]->setText("unknown");
            // 重置状态为unknown
            redStatus[uid]->setText("无");
            redStatus[uid]->setStyleSheet(""); // 清除背景色样式

            // 清除探测记录
            redDroneEverDetected[uid] = false;
            lastRedDroneDetectionTime.remove(uid);
            lastRedDroneInSightTime.remove(uid);
        }

        // 重置标志
        resetRedDroneUpdateLabels = false;
    }

    // 状态标签样式设置
    QString baseStyle = "min-width: 16px; min-height: 16px; max-width: 16px; max-height: 16px; border-radius: 8px;";

    // 遍历所有无人机信息并更新UI
    for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
        QString uid = it.key();
        DroneInfo info = it.value();

        // 根据无人机ID前缀判断是蓝方还是红方
        if (uid.startsWith("B")) {
            // 蓝方无人机
            if (blueHpBars.contains(uid)) {
                // 更新血量
                blueHpBars[uid]->setValue(info.hp);

                // 更新位置
                bluePoints[uid]->setText(QString("(%1,%2)").arg(info.x, 0, 'f', 0).arg(info.y, 0, 'f', 0));

                // 更新速度 - 如果坠毁则清零速度显示
                if (info.hp <= 0) {
                    blueVelocities[uid]->setText(QString("vx:00,vy:00"));
                    // 同时在数据结构中也清零速度值
                    dronesInfo[uid].vx = 0.0;
                    dronesInfo[uid].vy = 0.0;
                } else {
                    blueVelocities[uid]->setText(QString("vx:%1,vy:%2").arg(info.vx, 0, 'f', 0).arg(info.vy, 0, 'f', 0));
                }

                // 判断无人机状态并设置颜色
                QString statusColor = "green"; // 默认安全状态为绿色

                // 1. 检查是否坠毁
                if (info.hp <= 0) {
                    statusColor = "gray"; // 坠毁状态为灰色
                } else {
                    // 2. 检查是否在雷达范围内（紫色）
                    bool inRadarRange = false;
                    // 遍历所有障碍物，查找雷达
                    for (auto obstIt = obstaclesInfo.begin(); obstIt != obstaclesInfo.end(); ++obstIt) {
                        if (obstIt.value().type == "radar") {
                            // 计算与雷达的距离
                            double dx = info.x - obstIt.value().x;
                            double dy = info.y - obstIt.value().y;
                            double distance = sqrt(dx*dx + dy*dy);

                            if (distance <= 80) { // 雷达范围半径为80
                                inRadarRange = true;
                                statusColor = "purple";
                                break;
                            }
                        }
                    }

                    // 3. 检查是否在雷云范围内（黄色）
                    if (!inRadarRange) {
                        bool inCloudRange = false;
                        // 遍历所有障碍物，查找雷云
                        for (auto obstIt = obstaclesInfo.begin(); obstIt != obstaclesInfo.end(); ++obstIt) {
                            if (obstIt.value().type == "cloud") {
                                // 计算与雷云的距离
                                double dx = info.x - obstIt.value().x;
                                double dy = info.y - obstIt.value().y;
                                double distance = sqrt(dx*dx + dy*dy);

                                if (distance <= 80) { // 雷云范围半径为80
                                    inCloudRange = true;
                                    statusColor = "yellow";
                                    break;
                                }
                            }
                        }

                        // 4. 检查是否在敌方无人机攻击范围内（红色）
                        if (!inCloudRange) {
                            // 遍历所有红方无人机
                            for (auto droneIt = dronesInfo.begin(); droneIt != dronesInfo.end(); ++droneIt) {
                                if (droneIt.key().startsWith("R")) {
                                    // 计算与敌方无人机的距离
                                    double dx = info.x - droneIt.value().x;
                                    double dy = info.y - droneIt.value().y;
                                    double distance = sqrt(dx*dx + dy*dy);

                                    if (distance <= 150) { // 敌方攻击范围半径为150
                                        statusColor = "red";
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }

                // 设置圆形状态指示器
                blueStatus[uid]->setText(""); // 清空文本
                blueStatus[uid]->setStyleSheet(baseStyle + QString("background-color: %1;").arg(statusColor));
            }
        } else if (uid.startsWith("R")) {
            // 红方无人机
            if (redHpBars.contains(uid)) {
                // 更新血量
                redHpBars[uid]->setValue(info.hp);

                // 更新位置
                redPoints[uid]->setText(QString("(%1,%2)").arg(info.x, 0, 'f', 0).arg(info.y, 0, 'f', 0));

//                // 更新速度
//                redVelocities[uid]->setText(QString("vx:%1,vy:%2").arg(info.vx, 0, 'f', 2).arg(info.vy, 0, 'f', 2));

                // 判断无人机状态并设置颜色
                QString statusColor = "green"; // 默认安全状态为绿色

                // 检查是否坠毁
                if (info.hp <= 0) {
                    statusColor = "gray"; // 坠毁状态为灰色
                }

                // 设置圆形状态指示器
                redStatus[uid]->setText(""); // 清空文本
                redStatus[uid]->setStyleSheet(baseStyle + QString("background-color: %1;").arg(statusColor));

                // 更新探测时间
                // 标记该无人机已被探测到
                redDroneEverDetected[uid] = true;
                // 更新最后探测时间为当前时间
                lastRedDroneDetectionTime[uid] = currentTime;

                // 更新最后在视野中的时间（这个时间用于计算"多少秒前"）
                lastRedDroneInSightTime[uid] = currentTime;

                totalPauseSeconds = 0;

                // 当前正在探测到，显示"更新：刚刚"
                redUpdateTimes[uid]->setText("更新：刚刚");
            }
        }
    }

    // 检查是否有未更新的无人机，设置默认值
    // 蓝方无人机
    for (auto it = blueHpBars.begin(); it != blueHpBars.end(); ++it) {
        QString uid = it.key();
        if (!dronesInfo.contains(uid)) {
            // 无人机不存在或已被击毁，显示默认值
            blueHpBars[uid]->setValue(0);
            bluePoints[uid]->setText("(00,00)");
            blueVelocities[uid]->setText("vx:00,vy:00");

            // 设置为灰色圆点（坠毁状态）
            blueStatus[uid]->setText("");
            blueStatus[uid]->setStyleSheet(baseStyle + "background-color: gray;");
        }
    }

    // 红方无人机
    for (auto it = redHpBars.begin(); it != redHpBars.end(); ++it) {
        QString uid = it.key();
        if (!dronesInfo.contains(uid)) {
            // 无人机不在当前探测范围内

            // 如果曾经探测到过，使用上次记录的状态信息并显示时间
            if (redDroneEverDetected.value(uid, false)) {
                // 计算自上次探测到的时间差（秒）
                if (lastRedDroneInSightTime.contains(uid)) {
                    QDateTime currentTime = QDateTime::currentDateTime();

                    // 计算时间差（秒）
                    int secsPassed = lastRedDroneInSightTime[uid].secsTo(currentTime);

                    // 减去暂停的时间
                    if (serverPaused) {
                        // 如果当前是暂停状态，减去已上次检测到红方到现在累计的暂停时间和当前正在进行的暂停时间
                        int currentPauseTime = serverPauseStartTime.secsTo(currentTime);
                        secsPassed -= (totalPauseSeconds + currentPauseTime);
                    } else {
                        // 如果当前不是暂停状态，减去已上次检测到红方到现在累计的所有暂停时间
                        secsPassed -= totalPauseSeconds;
                    }

                    // 确保时间不为负数
                    if (secsPassed < 0) {
                        secsPassed = 0;
                    }

                    // 更新显示
                    redUpdateTimes[uid]->setText(QString("更新：%1s前").arg(secsPassed));
                } else {
                    // 如果没有记录最后在视野中的时间（不应该发生），显示未更新
                    redUpdateTimes[uid]->setText("未更新");
                }
            } else {
                // 如果从未探测到过，显示未更新
                redUpdateTimes[uid]->setText("未更新");

                redStatus[uid]->setText("");
                redStatus[uid]->setStyleSheet(baseStyle + "background-color: gray;"); // 清除背景色样式
            }
        }
    }
    ui->time_status->setText(QString("游戏状态: %1               剩余时间: %2秒").arg(gameStage).arg(gameLeftTime));
    // 计算胜率
    int totalBlueHP = 0;
    int totalRedHP = 0;
    int blueCount = 0;
    int redCount = 0;

    // 遍历所有无人机，计算总血量
    for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
        QString uid = it.key();
        DroneInfo info = it.value();

        if (uid.startsWith("B")) {
            totalBlueHP += info.hp;
            blueCount++;
        } else if (uid.startsWith("R")) {
            totalRedHP += info.hp;
            redCount++;
        }
    }

    // 计算胜率
    int winRate = 100; // 默认胜率为100%

    // 只有当红方有血量时才计算胜率
    if (totalRedHP > 0) {
        // 计算蓝方和红方的平均血量
        double blueAvgHP = (blueCount > 0) ? (double)totalBlueHP / blueCount : 0;
        double redAvgHP = (redCount > 0) ? (double)totalRedHP / redCount : 0;

        // 计算胜率 = 蓝方平均血量 / (蓝方平均血量 + 红方平均血量) * 100%
        if (blueAvgHP + redAvgHP > 0) {
            winRate = qRound(blueAvgHP / (blueAvgHP + redAvgHP) * 100);
        } else {
            winRate = 50; // 如果双方血量都为0，则胜率为50%
        }
    }

    // 更新胜率显示
    ui->win_rate->setText(QString("%1%").arg(winRate));
}

//*****************策略1、策略3使用 A* 算法公用槽函数等****************
// 处理目标点设置 - 支持指定无人机ID
void MainWindow::onTargetPointSet(const QPoint& targetPoint, const QString& droneId) {
    // 如果游戏状态为finish，则不处理路径规划请求
    if (gameStage == "finish"||gameStage == "init") {
        appendLog("游戏已结束或未开始，无法为无人机 "+droneId+" 规划路径");
        return;
    }
    // 将点击位置设为终点
    m_targetPoint = targetPoint;
    m_hasValidTargetPoint = true;
    appendLog(QString("手动为无人机 %1 设置目标点:(%2,%3)").arg(droneId).arg(m_targetPoint.x()).arg(m_targetPoint.y()));

    // 清除指定无人机的路径
    gridMap->clearPath(droneId);

    if (!droneId.isEmpty() && dronesInfo.contains(droneId) && dronesInfo[droneId].hp > 0) {
        // 使用指定的无人机作为起点
        int gridCol = dronesInfo[droneId].x / gridMap->GRID_SIZE;
        int gridRow = dronesInfo[droneId].y / gridMap->GRID_SIZE;
        m_startPoint = QPoint(gridCol, gridRow);

    } else {
        // 如果指定的无人机不可用，使用第一个找到的蓝方无人机
        for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
            const QString &uid = it.key();
            const DroneInfo &info = it.value();

            if (uid.startsWith("B") && info.hp > 0) {
                // 将无人机位置转换为栅格坐标
                int gridCol = info.x / gridMap->GRID_SIZE;
                int gridRow = info.y / gridMap->GRID_SIZE;
                m_startPoint = QPoint(gridCol, gridRow);
                break; // 只使用第一个找到的蓝方无人机
            }
        }
    }

    // 更新地图
    emit UpdateSharedGridMap(gridMap->getSharedGridMap());

    // 调用路径规划
    emit StartfindPath(m_startPoint, m_targetPoint, droneId);
}

// 处理路径规划完成 - 支持指定无人机ID
void MainWindow::onPathPlanned(const QVector<QPoint>& path, const QString& droneId) {
    if(currentStrategy == 1){
            appendLog(QString("无人机 %1 路径规划完成，路径点数量:%2").arg(droneId).arg(path.size()));
    }

    // 在地图上显示路径，使用传入的无人机ID
    gridMap->setPath(path, droneId);

    // 重置起点和终点标志，准备下一次规划
    m_hasValidStartPoint = false;
    m_hasValidTargetPoint = false;
}

// 初始化PathPlanner
void MainWindow::initializePathPlanner() {
    connect(gridMap, &GridMap::targetReached, this, &MainWindow::onTargetReached);
    if (!isPathPlannerInitialized) {
        // 创建PathPlanner实例
        pathPlanner = new PathPlanner(this);

        // 连接信号和槽，注意使用新的带有无人机ID参数的信号
        connect(gridMap, &GridMap::targetPointSet, this, &MainWindow::onTargetPointSet);
        connect(this, &MainWindow::StartfindPath, pathPlanner, &PathPlanner::onStartfindPath);
        connect(this, &MainWindow::UpdateSharedGridMap, pathPlanner, &PathPlanner::onUpdateSharedGridMap);
        connect(pathPlanner, &PathPlanner::pathPlanned, this, &MainWindow::onPathPlanned);
        connect(gridMap, &GridMap::mapUpdated, pathPlanner, &PathPlanner::onMapUpdated);
        connect(gridMap, &GridMap::mapReset, pathPlanner, &PathPlanner::onMapReset);

        // 设置共享地图
        pathPlanner->setSharedGridMap(gridMap->getSharedGridMap());

        isPathPlannerInitialized = true;
        appendLog(QString("PathPlanner文件A*相关算法资源初始化完成!"));
    }
}
// 清理PathPlanner
void MainWindow::cleanupPathPlanner() {
    if (isPathPlannerInitialized) {
        // 断开信号连接
        disconnect(gridMap, &GridMap::targetPointSet, this, &MainWindow::onTargetPointSet);
        disconnect(this, &MainWindow::StartfindPath, pathPlanner, &PathPlanner::onStartfindPath);
        disconnect(this, &MainWindow::UpdateSharedGridMap, pathPlanner, &PathPlanner::onUpdateSharedGridMap);
        disconnect(pathPlanner, &PathPlanner::pathPlanned, this, &MainWindow::onPathPlanned);
        disconnect(gridMap, &GridMap::mapUpdated, pathPlanner, &PathPlanner::onMapUpdated);
        disconnect(gridMap, &GridMap::mapReset, pathPlanner, &PathPlanner::onMapReset);

        // 清除路径
        gridMap->clearPath();

        // 删除PathPlanner实例
        delete pathPlanner;
        pathPlanner = nullptr;

        isPathPlannerInitialized = false;
        qDebug() << "PathPlanner资源已清理";
    }
}

//使用预设目标点进行路径规划循环巡逻
void MainWindow::planPathToPresetTargets() {
    // 检查游戏是否正在运行
    if (gameStage != "running") {
        qDebug() <<"游戏未运行，无法规划路径";
        return;
    }

    QStringList blueUAVs = {"B1", "B2", "B3"};

    // 首先更新共享地图，所有无人机共用同一份地图数据
    emit UpdateSharedGridMap(gridMap->getSharedGridMap());

    // 使用QtConcurrent::run并行处理每个无人机的路径规划
    for (const QString& droneId : blueUAVs) {
        if (!dronesInfo.contains(droneId) || dronesInfo[droneId].hp <= 0) {
            continue;
        }

        // 使用QtConcurrent::run在单独的线程中处理每个无人机的路径规划请求
        QtConcurrent::run([=]() {
            int gridCol = dronesInfo[droneId].x / gridMap->GRID_SIZE;
            int gridRow = dronesInfo[droneId].y / gridMap->GRID_SIZE;
            QPoint startPoint(gridCol, gridRow);

            // 确定目标点 - 先尝试追踪，若无法追踪则巡逻
            QPoint targetPoint;

            // 游戏开始先进入巡逻模式
            targetPoint = targetManager->getPatrolPoint(droneId);

            // 在主线程中执行UI相关操作
            QMetaObject::invokeMethod(this, [=]() {
                gridMap->clearPath(droneId);

                // 发送路径规划请求
                emit StartfindPath(startPoint, targetPoint, droneId);
            }, Qt::QueuedConnection);
        });
    }
}

bool MainWindow::isPathInObstacle(const QString &droneId) {
    // 如果无人机没有路径，返回false
    if (!gridMap->m_smoothedPathMap.contains(droneId) ||
        gridMap->m_smoothedPathMap[droneId].size() <= 1) {
        return false;
    }

    // 获取无人机的平滑路径
    const QVector<QPointF> &path = gridMap->m_smoothedPathMap[droneId];

    // 获取无人机当前位置
    if (!dronesInfo.contains(droneId)) {
        return false; // 无人机信息不存在
    }
    QPointF currentPos(dronesInfo[droneId].x, dronesInfo[droneId].y);

    // 找到距离当前位置最近的路径点索引
    int currentIndex = gridMap->findClosestPathPointIndex(currentPos, droneId);

    // 遍历所有障碍物
    for (auto it = staclePositions.begin(); it != staclePositions.end(); ++it) {
        const stacleInfo &obstacle = it.value();

        // 只检查当前位置之后的路径点（未飞行的路径）
        for (int i = currentIndex; i < path.size(); i++) {
            const QPointF &point = path[i];
            // 使用GridMap的isPointInCircle方法检查点是否在障碍物范围内
            if (gridMap->isPointInCircle(point.x(), point.y(), obstacle.x, obstacle.y, obstacle.radius)) {
                return true; // 未飞行的路径与障碍物相交
            }
        }
    }

    return false; // 未飞行的路径不与任何障碍物相交
}


void MainWindow::onTargetReached(const QString& droneId) {
    // 更新TargetManager中的状态
    targetManager->setTargetReached(droneId, true);

    // 只为到达终点的这个无人机重新规划路径
    if(currentStrategy == 1)
    {
        appendLog("策略1：无人机 "+droneId+" 已到达目标点，准备规划下一个目标");
        planPathForSingleDrone_S1(droneId);
    }else if(currentStrategy == 3){
        appendLog("策略3：无人机 "+droneId+" 已到达目标点，准备规划下一个目标");
        planPathForSingleDrone_S3(droneId);
    }
}
//*****************策略1 A* 算法进行路径规划和动态避障****************
void MainWindow::planPathForSingleDrone_S1(const QString &droneId) {
    // 检查游戏是否正在运行
    if (gameStage != "running") {
        qDebug() << "游戏未运行，无法规划路径";
        return;
    }

    // 检查该无人机是否存在且血量大于0
    if (!dronesInfo.contains(droneId) || dronesInfo[droneId].hp <= 0) {
        return;
    }

    // 更新共享地图
    emit UpdateSharedGridMap(gridMap->getSharedGridMap());

    // 检查敌方无人机位置并更新到SO1
    QMap<QString, QPoint> enemyPositions;
    QMap<QString, int> enemyHp;
    bool hasEnemyUAV = false;

    for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
        const QString &id = it.key();
        const DroneInfo &info = it.value();

        if (id.startsWith("R") && info.hp > 0) {
            // 转换为栅格坐标
            QPoint gridPos(info.x / gridMap->GRID_SIZE, info.y / gridMap->GRID_SIZE);
            enemyPositions[id] = gridPos;
            enemyHp[id] = info.hp;
            hasEnemyUAV = true;

            // 如果检测到了R1，R2，R3，则切换到追踪模式
            if (id == "R1"||id == "R2"||id == "R3") {
                SO1->setTrackingMode(true);
            }
        }
    }

    // 更新敌方无人机位置和血量到SO1
    SO1->updateEnemyInfo(enemyPositions, enemyHp);

    // 如果没有检测到敌方无人机，切换回巡逻模式
    if (!hasEnemyUAV) {
        SO1->setTrackingMode(false);
    }

    // 使用QtConcurrent::run在单独的线程中处理该无人机的路径规划请求
    QtConcurrent::run([=]() {
        int gridCol = dronesInfo[droneId].x / gridMap->GRID_SIZE;
        int gridRow = dronesInfo[droneId].y / gridMap->GRID_SIZE;
        QPoint startPoint(gridCol, gridRow);

        // 确定目标点 - 先尝试追踪，若无法追踪则巡逻
        QPoint targetPoint;
        if (SO1->isDroneTracking(droneId)) {
             // 尝试获取追踪目标
            QPoint trackingTarget = SO1->getBestTrackingTarget(droneId);

            if (trackingTarget != QPoint(0, 0)) {
                // 有可跟踪目标
                targetPoint =trackingTarget;
                qDebug() << droneId << "正在追踪敌方无人机，目标点:" << targetPoint;
//                appendLog(QString("策略1：无人机 %1 正在追踪敌方无人机，目标点:(%2,%3)").arg(droneId).arg(targetPoint.x()).arg(targetPoint.y()));
            } else {
                // 无可跟踪目标，切换到巡逻+
                targetPoint = targetManager->getPatrolPoint(droneId);
                qDebug() << droneId << "无法追踪敌方，切换到巡逻模式，目标点:" << targetPoint;
//                appendLog(QString("策略1：无人机 %1 无法追踪敌方，切换到巡逻模式，目标点:(%2,%3)").arg(droneId).arg(targetPoint.x()).arg(targetPoint.y()));
            }
        } else {
            // 巡逻模式
            targetPoint = targetManager->getPatrolPoint(droneId);
        }

        // 在主线程中执行UI相关操作和发送信号
        QMetaObject::invokeMethod(this, [=]() {
            gridMap->clearPath(droneId);

            // 发送路径规划请求
            emit StartfindPath(startPoint, targetPoint, droneId);
        }, Qt::QueuedConnection);
    });
}


//*****************策略3 A* 算法进行路径规划和动态避障的蛇形策略****************
void MainWindow::planPathForSingleDrone_S3(const QString &droneId) { // ！策略3路径规划主函数
    // 检查游戏是否正在运行
    if (gameStage != "running") {
        qDebug() << "游戏未运行，无法为 " << droneId << " 规划路径";
        return;
    }

    // 检查该无人机是否存在且血量大于0
    if (!dronesInfo.contains(droneId) || dronesInfo[droneId].hp <= 0) {
        return;
    }

    // 只为B1规划路径，B2和B3直接跟随B1，不需要规划路径
    if (droneId != "B1") {
        qDebug() << "[Strategy3] 收到" << droneId << "到达目标点的通知，但在蛇形模式下只为B1规划路径";
        return;
    }

    // 总是先更新战场态势
    if (SO3) {
        QMap<QString, DroneState> friendlyStates;
        QMap<QString, DroneState> enemyStates;
        for (const auto& drone : dronesInfo.keys()) {
            if (drone.startsWith("B")) {
                friendlyStates[drone] = {QPoint(dronesInfo[drone].x, dronesInfo[drone].y), dronesInfo[drone].hp};
            } else if (drone.startsWith("R")) {
                enemyStates[drone] = {QPoint(dronesInfo[drone].x, dronesInfo[drone].y), dronesInfo[drone].hp};
            }
        }
        SO3->updateGameState(friendlyStates, enemyStates);
    }

    // 更新共享地图
    emit UpdateSharedGridMap(gridMap->getSharedGridMap());

    // 使用QtConcurrent::run在单独的线程中处理该无人机的路径规划请求
    QtConcurrent::run([=]() {
        int gridCol = dronesInfo[droneId].x / gridMap->GRID_SIZE;
        int gridRow = dronesInfo[droneId].y / gridMap->GRID_SIZE;
        QPoint startPoint(gridCol, gridRow);

        // 从策略3获取目标点 - 直接返回栅格坐标
        QPoint gridTargetPoint = SO3->getTargetForDrone(droneId);

        if (gridTargetPoint == QPoint(0,0)) {
            // 如果策略没有给出有效目标（可能因为没有敌人了），则让其原地待命或巡逻
            gridTargetPoint = targetManager->getPatrolPoint(droneId);
            qDebug() << "[Strategy3] " << droneId << " 没有有效目标，回退到巡逻模式:" << gridTargetPoint;
        } else {
            qDebug() << "[Strategy3] " << droneId << " 规划路径到栅格目标:" << gridTargetPoint;
        }

        // 确保目标点在地图范围内
        gridTargetPoint.setX(qBound(0, gridTargetPoint.x(), gridMap->GRID_COLS - 1));
        gridTargetPoint.setY(qBound(0, gridTargetPoint.y(), gridMap->GRID_ROWS - 1));

        // 使用SO3的adjustTargetPoint方法确保目标点不在障碍物区域内
        gridTargetPoint = SO3->adjustTargetPoint(gridTargetPoint, true);

        // 在主线程中执行UI相关操作和发送信号
        QMetaObject::invokeMethod(this, [=]() {
            // 清除旧路径但保留目标点
            gridMap->clearPath(droneId);

            // 发送路径规划请求 - 栅格坐标
            emit StartfindPath(startPoint, gridTargetPoint, droneId);
        }, Qt::QueuedConnection);
    });
}

// 处理Strategy3的needReplanPath信号
void MainWindow::onStrategy3NeedReplanPath(QString droneId, QPoint current, QPoint target) // ！策略3重规划槽函数
{
    if (currentStrategy != 3 || gameStage != "running") {
        return;
    }

    // 确定当前的蛇头
    QString snakeHead = "";
    if (dronesInfo.contains("B1") && dronesInfo["B1"].hp > 0) {
        snakeHead = "B1";  // B1优先作为蛇头
    } else if (dronesInfo.contains("B2") && dronesInfo["B2"].hp > 0) {
        snakeHead = "B2";  // B1坠毁时B2成为蛇头
    } else if (dronesInfo.contains("B3") && dronesInfo["B3"].hp > 0) {
        snakeHead = "B3";  // B1和B2坠毁时B3成为蛇头
    }

    // 只为蛇头规划路径
    if (droneId == snakeHead) {
        qDebug() << "[Strategy3] 收到needReplanPath信号，为无人机" << droneId << "规划路径，从" << current << "到" << target;

        // 检查该无人机是否存在且血量大于0
        if (!dronesInfo.contains(droneId) || dronesInfo[droneId].hp <= 0) {
//            qDebug() << "[Strategy3] 无人机" << droneId << "不存在或已坠毁，无法规划路径";
            return;
        }

        // 获取无人机当前位置的栅格坐标
        int gridCol = dronesInfo[droneId].x / gridMap->GRID_SIZE;
        int gridRow = dronesInfo[droneId].y / gridMap->GRID_SIZE;
        QPoint startPoint(gridCol, gridRow);

        // 将目标点从像素坐标转换为栅格坐标
        int targetGridCol = target.x() / gridMap->GRID_SIZE;
        int targetGridRow = target.y() / gridMap->GRID_SIZE;
        QPoint gridTargetPoint(targetGridCol, targetGridRow);

        // 确保栅格坐标在有效范围内
        gridTargetPoint.setX(qBound(0, gridTargetPoint.x(), gridMap->GRID_COLS - 1));
        gridTargetPoint.setY(qBound(0, gridTargetPoint.y(), gridMap->GRID_ROWS - 1));

        // 使用SO3的adjustTargetPoint方法确保目标点不在障碍物区域
        gridTargetPoint = SO3->adjustTargetPoint(gridTargetPoint, true);

//        qDebug() << "[Strategy3] " << droneId << " 规划路径到栅格目标: " << gridTargetPoint;
//        qDebug() << "[Strategy3] " << droneId << " 经调整后的栅格目标点: " << gridTargetPoint;

        // 检查当前位置和目标位置是否相同，如果相同则不需要规划
        if (startPoint == gridTargetPoint) {
//            qDebug() << "[Strategy3] 当前位置已经是目标位置，无需规划路径";
            return;
        }

        // 在主线程中执行UI相关操作和发送信号
        QMetaObject::invokeMethod(this, [=]() {
            gridMap->clearPath(droneId);
            // 发送路径规划请求 - 栅格坐标
            emit StartfindPath(startPoint, gridTargetPoint, droneId);
        }, Qt::QueuedConnection);
    } else {
//        qDebug() << "[Strategy3] 忽略非蛇头无人机" << droneId << "的路径规划请求";
    }
}

// 为策略3的单个无人机规划路径 - 作为直接规划路径的辅助函数
void MainWindow::planPathForSingleDrone_S3(const QString& droneId, const QPoint& currentPoint, const QPoint& targetPoint)
{
    // 检查是否正在运行
    if (gameStage != "running") {
        qDebug() << "[Strategy3] 游戏未运行，无法规划路径";
        return;
    }

    // 检查该无人机是否存在且血量大于0
    if (!dronesInfo.contains(droneId) || dronesInfo[droneId].hp <= 0) {
        qDebug() << "[Strategy3] 无人机" << droneId << "不存在或已坠毁，无法规划路径";
        return;
    }

    // 获取无人机当前位置的栅格坐标 - 使用实时位置而不是传入的参数
    int gridCol = dronesInfo[droneId].x / gridMap->GRID_SIZE;
    int gridRow = dronesInfo[droneId].y / gridMap->GRID_SIZE;
    QPoint startPoint(gridCol, gridRow);

    // 将目标点从像素坐标转换为栅格坐标
    int targetGridCol = targetPoint.x() / gridMap->GRID_SIZE;
    int targetGridRow = targetPoint.y() / gridMap->GRID_SIZE;
    QPoint gridTargetPoint(targetGridCol, targetGridRow);

    // 确保栅格坐标在有效范围内
    gridTargetPoint.setX(qBound(0, gridTargetPoint.x(), gridMap->GRID_COLS - 1));
    gridTargetPoint.setY(qBound(0, gridTargetPoint.y(), gridMap->GRID_ROWS - 1));

    // 使用SO3的adjustTargetPoint方法确保目标点不在障碍物区域
    gridTargetPoint = SO3->adjustTargetPoint(gridTargetPoint, true);

//    qDebug() << "[Strategy3] " << droneId << " 规划路径到栅格目标: " << gridTargetPoint;
//    qDebug() << "[Strategy3] " << droneId << " 经调整后的栅格目标点: " << gridTargetPoint;

    // 检查当前位置和目标位置是否相同，如果相同则不需要规划
    if (startPoint == gridTargetPoint) {
//        qDebug() << "[Strategy3] 当前位置已经是目标位置，无需规划路径";
        return;
    }

    // 清除之前的路径并规划新路径
    gridMap->clearPath(droneId);
    emit StartfindPath(startPoint, gridTargetPoint, droneId);
}

bool MainWindow::isPathInObstacle(const QString &droneId, const QList<QPoint> &path)
{
    // 策略3只检查蛇头的路径
    if (currentStrategy == 3) {
        // 确定当前的蛇头
        QString snakeHead = "";
        if (dronesInfo.contains("B1") && dronesInfo["B1"].hp > 0) {
            snakeHead = "B1";  // B1优先作为蛇头
        } else if (dronesInfo.contains("B2") && dronesInfo["B2"].hp > 0) {
            snakeHead = "B2";  // B1坠毁时B2成为蛇头
        } else if (dronesInfo.contains("B3") && dronesInfo["B3"].hp > 0) {
            snakeHead = "B3";  // B1和B2坠毁时B3成为蛇头
        }

        // 只检查蛇头的路径
        if (droneId != snakeHead) {
            return false;
        }
    }

    // 检查路径是否穿过障碍物
    for (const QPoint& gridPoint : path) {
        // 遍历所有障碍物，检查路径点是否在障碍物内
        for (auto it = obstaclesInfo.begin(); it != obstaclesInfo.end(); ++it) {
            const ObstacleInfo &obstacle = it.value();
            if (gridMap->isPointInCircle(
                gridPoint.x() * gridMap->GRID_SIZE,
                gridPoint.y() * gridMap->GRID_SIZE,
                obstacle.x, obstacle.y, obstacle.r)) {
                return true;
            }
        }
    }

    return false;
}

// 为策略3的跟随者无人机处理障碍物避障
void MainWindow::handleFollowerObstacleAvoidance(const QString& followerId, const QString& snakeHead) {
    // 检查参数有效性
    if (followerId.isEmpty() || snakeHead.isEmpty() || !dronesInfo.contains(followerId) || !dronesInfo.contains(snakeHead)) {
//        qDebug() << "[Strategy3] 避障参数无效，无法为跟随者" << followerId << "规划避障路径";
        return;
    }

    // 获取跟随者当前位置
    QPointF followerPos(dronesInfo[followerId].x, dronesInfo[followerId].y);

    // 获取跟随者当前栅格坐标
    int followerGridCol = followerPos.x() / gridMap->GRID_SIZE;
    int followerGridRow = followerPos.y() / gridMap->GRID_SIZE;
    QPoint followerGridPoint(followerGridCol, followerGridRow);

    // 确定路径源 - 应该使用当前蛇头的路径
    QString pathSource = snakeHead;

    // 检查蛇头是否有有效路径
    if (!gridMap->m_smoothedPathMap.contains(pathSource) || gridMap->m_smoothedPathMap[pathSource].size() <= 1) {
//        qDebug() << "[Strategy3] 蛇头" << snakeHead << "没有有效路径，无法为跟随者" << followerId << "规划避障路径";
        return;
    }

    // 找到蛇头当前在路径上的位置索引
    int headIndex = gridMap->findClosestPathPointIndex(
        QPointF(dronesInfo[pathSource].x, dronesInfo[pathSource].y),
        pathSource);

    // 确定跟随者应该跟随的目标点索引
    int targetIndex;
    if (followerId == "B2") {
        targetIndex = qMax(0, headIndex - 80);
    } else if (followerId == "B3") {
        // 如果B2存活且不是蛇头，则B3跟随B2（落后160个点）
        if (dronesInfo.contains("B2") && dronesInfo["B2"].hp > 0 && snakeHead != "B2") {
            targetIndex = qMax(0, headIndex - 160);
        } else {
            // 否则B3直接跟随蛇头，落后80个点
            targetIndex = qMax(0, headIndex - 80);
        }
    } else {
        // 未知的跟随者ID
//        qDebug() << "[Strategy3] 未知的跟随者ID" << followerId;
        return;
    }

    // 获取目标点（像素坐标）
    QPointF targetPixelPoint = gridMap->m_smoothedPathMap[pathSource][targetIndex];

    // 转换为栅格坐标
    QPoint targetGridPoint(
        targetPixelPoint.x() / gridMap->GRID_SIZE,
        targetPixelPoint.y() / gridMap->GRID_SIZE
    );

    // 检查是否需要避障
    // 1. 首先尝试找到一个安全的路径点
    QPoint safePoint = findSafePointNearTarget(followerGridPoint, targetGridPoint);

//    qDebug() << "[Strategy3] 跟随者" << followerId << "避障：从" << followerGridPoint
//             << "到" << targetGridPoint << "，安全点：" << safePoint << "，路径源:" << pathSource;

    // 清除旧路径
    gridMap->clearPath(followerId);

    // 规划新路径到安全点
    emit StartfindPath(followerGridPoint, safePoint, followerId);

    // 添加一个连接，当路径规划完成后立即执行移动
    QMetaObject::Connection* conn = new QMetaObject::Connection();
    *conn = connect(pathPlanner, &PathPlanner::pathPlanned, this, [this, followerId, conn](const QVector<QPoint>& path, const QString& droneId) {
        // 确保这是我们正在等待的无人机的路径
        if (droneId == followerId) {
            // 断开连接，避免重复处理
            disconnect(*conn);
            delete conn;

            // 检查路径是否有效
            if (!path.isEmpty() && gridMap->m_smoothedPathMap.contains(droneId) && gridMap->m_smoothedPathMap[droneId].size() > 1) {
                // 获取无人机当前位置
                QPointF currentPos(dronesInfo[droneId].x, dronesInfo[droneId].y);

                // 使用GridMap计算速度
                QPointF velocity = gridMap->calculateVelocity(droneId, currentPos);

//                qDebug() << "[Strategy3] 避障后计算" << droneId << "的速度:" << velocity;

                // 发送控制命令
                sendControlCommand(droneId, velocity);
            } else {
//                qDebug() << "[Strategy3] 避障路径规划失败或路径无效，" << droneId << "保持静止";
                QPointF velocity(0, 0);
                sendControlCommand(droneId, velocity);
            }
        }
    });
}

// 在目标点附近寻找安全点（无障碍物）
QPoint MainWindow::findSafePointNearTarget(const QPoint& start, const QPoint& target) {
    // 首先检查目标点本身是否安全
    if (!isGridPointInObstacle(target)) {
        return target; // 目标点安全，直接返回
    }

    // 如果目标点不安全，在其周围寻找安全点
    // 定义搜索半径和方向
    const int MAX_RADIUS = 5; // 最大搜索半径
    const int DIRECTIONS = 8; // 8个方向
    const int dx[8] = {1, 1, 0, -1, -1, -1, 0, 1};
    const int dy[8] = {0, 1, 1, 1, 0, -1, -1, -1};

    // 按照距离从近到远搜索
    for (int radius = 1; radius <= MAX_RADIUS; radius++) {
        // 对于每个半径，检查所有方向
        for (int dir = 0; dir < DIRECTIONS; dir++) {
            // 计算候选点
            QPoint candidate(
                target.x() + dx[dir] * radius,
                target.y() + dy[dir] * radius
            );

            // 确保候选点在地图范围内
            if (candidate.x() < 0 || candidate.x() >= gridMap->GRID_COLS ||
                candidate.y() < 0 || candidate.y() >= gridMap->GRID_ROWS) {
                continue; // 超出地图范围，跳过
            }

            // 检查候选点是否安全
            if (!isGridPointInObstacle(candidate)) {
                return candidate; // 找到安全点，返回
            }
        }
    }

    // 如果在目标点周围没有找到安全点，则返回起点（保持原位）
    return start;
}

// 检查栅格点是否在任何障碍物内
bool MainWindow::isGridPointInObstacle(const QPoint& gridPoint) {
    // 转换为像素坐标（使用栅格中心点）
    float pixelX = gridPoint.x() * gridMap->GRID_SIZE + gridMap->GRID_SIZE / 2.0f;
    float pixelY = gridPoint.y() * gridMap->GRID_SIZE + gridMap->GRID_SIZE / 2.0f;

    // 检查是否在任何障碍物内
    for (auto it = obstaclesInfo.begin(); it != obstaclesInfo.end(); ++it) {
        const ObstacleInfo &obstacle = it.value();
        if (gridMap->isPointInCircle(pixelX, pixelY, obstacle.x, obstacle.y, obstacle.r)) {
            return true; // 在障碍物内
        }
    }

    // 检查是否在任何静态障碍物内
    for (auto it = staticObstacles.begin(); it != staticObstacles.end(); ++it) {
        const ObstacleInfo &obstacle = it.value();
        if (gridMap->isPointInCircle(pixelX, pixelY, obstacle.x, obstacle.y, obstacle.r)) {
            return true; // 在障碍物内
        }
    }

    // 检查是否在任何移动障碍物内
    for (auto it = movingObstacles.begin(); it != movingObstacles.end(); ++it) {
        const ObstacleInfo &obstacle = it.value();
        if (gridMap->isPointInCircle(pixelX, pixelY, obstacle.x, obstacle.y, obstacle.r)) {
            return true; // 在障碍物内
        }
    }

    return false; // 不在任何障碍物内
}

// 获取两点之间的直线上的所有栅格点
QList<QPoint> MainWindow::getLinePoints(const QPoint& start, const QPoint& end) {
    QList<QPoint> points;

    // 使用Bresenham算法获取两点之间的直线上的所有点
    int x1 = start.x();
    int y1 = start.y();
    int x2 = end.x();
    int y2 = end.y();

    int dx = abs(x2 - x1);
    int dy = abs(y2 - y1);
    int sx = (x1 < x2) ? 1 : -1;
    int sy = (y1 < y2) ? 1 : -1;
    int err = dx - dy;

    while (true) {
        points.append(QPoint(x1, y1));

        if (x1 == x2 && y1 == y2) {
            break;
        }

        int e2 = 2 * err;
        if (e2 > -dy) {
            err -= dy;
            x1 += sx;
        }
        if (e2 < dx) {
            err += dx;
            y1 += sy;
        }
    }

    return points;
}
//*******************************************************



// 构建18维向量的方法
QVector<float> MainWindow::buildObservationVector()
{
    QVector<float> observation(18, 0.0f);

    // 前9个元素：我方无人机B1, B2, B3的栅格位置和血量
    QStringList blueUAVs = {"B1", "B2", "B3"};
    for (int i = 0; i < 3; ++i) {
        const QString &droneId = blueUAVs[i];
        int baseIndex = i * 3;

        if (dronesInfo.contains(droneId) && dronesInfo[droneId].hp > 0) {
            // 无人机存在且血量大于0
            observation[baseIndex] = dronesInfo[droneId].x / gridMap->GRID_SIZE;     // gridCol
            observation[baseIndex + 1] = dronesInfo[droneId].y / gridMap->GRID_SIZE; // gridRow
            observation[baseIndex + 2] = dronesInfo[droneId].hp;                     // hp
        } else {
            // 无人机坠机，用0补齐
            observation[baseIndex] = 0.0f;
            observation[baseIndex + 1] = 0.0f;
            observation[baseIndex + 2] = 0.0f;
        }
    }

    // 后9个元素：敌方无人机R1, R2, R3的栅格位置和血量
    QStringList redUAVs = {"R1", "R2", "R3"};
    for (int i = 0; i < 3; ++i) {
        const QString &droneId = redUAVs[i];
        int baseIndex = 9 + i * 3;

        if (dronesInfo.contains(droneId) && dronesInfo[droneId].hp > 0) {
            // 当前探测到敌方无人机且血量大于0
            observation[baseIndex] = dronesInfo[droneId].x / gridMap->GRID_SIZE;     // gridCol
            observation[baseIndex + 1] = dronesInfo[droneId].y / gridMap->GRID_SIZE; // gridRow
            observation[baseIndex + 2] = dronesInfo[droneId].hp;                     // hp
        } else {
            // 未探测到敌方无人机，设置为-1
            observation[baseIndex] = -1.0f;
            observation[baseIndex + 1] = -1.0f;
            observation[baseIndex + 2] = -1.0f;
        }
    }

    return observation;
}

//*****************算法2 maddpg算法uavmodal相关的槽函数****************

QVector<float> MainWindow::buildObservationForUAV(const QString &uavId)
{
    if (!dronesInfo.contains(uavId)) {
        return QVector<float>();
    }

    const DroneInfo &self = dronesInfo[uavId];
    QPointF selfPos(self.x, self.y);
    QPointF selfVel(self.vx, self.vy);

    // 友方信息
    QVector<QPointF> friendlyPos;
    QVector<float> friendlyHp;
    for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
        const QString &id = it.key();
        const DroneInfo &info = it.value();
        if (id.startsWith("B") && id != uavId && info.hp > 0) {
            friendlyPos.append(QPointF(info.x, info.y));
            friendlyHp.append(info.hp / 100.0f);
        }
    }

    // 敌方信息
    QVector<QPointF> enemyPos;
    QVector<float> enemyHp;
    for (auto it = dronesInfo.begin(); it != dronesInfo.end(); ++it) {
        const QString &id = it.key();
        const DroneInfo &info = it.value();
        if (id.startsWith("R") && info.hp > 0) {
            enemyPos.append(QPointF(info.x, info.y));
            enemyHp.append(info.hp / 100.0f);
        }
    }
    // 障碍信息
    QVector<QPointF> obstaclePos;
    QVector<float> obstacleRadius;
    QVector<QPointF> obstacleVelocity;
    QVector<float> obstacleDistances; // 存储障碍物距离，用于排序

//    qDebug() << QString("=== 当前自身位置: (%1, %2) ===").arg(selfPos.x(), 0, 'f', 1).arg(selfPos.y(), 0, 'f', 1);
//    qDebug() << QString("obstaclesInfo总数: %1").arg(obstaclesInfo.size());

    for (auto it = obstaclesInfo.begin(); it != obstaclesInfo.end(); ++it) {
       const ObstacleInfo &info = it.value();
       if (info.type == "radar" || info.type == "cloud"||info.type == "mountain") {
           QPointF velocity(0, 0); // 默认速度为0

           // 如果是雷云，获取速度信息
           if (info.type == "cloud" && cloudVelocities.contains(info.id) &&
               cloudVelocities[info.id].hasValidVelocity) {
               velocity = cloudVelocities[info.id].velocity;
           }

           // 计算与无人机的距离
           float dx = info.x - selfPos.x();
           float dy = info.y - selfPos.y();
           float distance = sqrt(dx*dx + dy*dy);

           // 添加详细调试
//           qDebug() << QString("原始障碍物 ID=%1, 类型=%2: 位置(%3, %4), 距离=%5")
//                       .arg(info.id)
//                       .arg(info.type)
//                       .arg(info.x, 0, 'f', 1)
//                       .arg(info.y, 0, 'f', 1)
//                       .arg(distance, 0, 'f', 1);

           // 添加障碍物信息
           obstaclePos.append(QPointF(info.x, info.y));
           obstacleRadius.append(info.r / 80.0f);
           obstacleVelocity.append(velocity);
           obstacleDistances.append(distance); // 存储距离用于排序
       }
    }

//    qDebug() << QString("有效障碍物数量: %1").arg(obstaclePos.size());

    // 按距离排序障碍物
    QVector<int> indices;
    for (int i = 0; i < obstaclePos.size(); i++) {
       indices.append(i);
    }

    // 使用lambda表达式按距离排序
    std::sort(indices.begin(), indices.end(), [&](int a, int b) {
       return obstacleDistances[a] < obstacleDistances[b];
    });

    // 显示排序结果
//    qDebug() << "=== 排序后的障碍物 ===";
    for (int i = 0; i < indices.size(); i++) {
        int idx = indices[i];
//        qDebug() << QString("排序第%1: 索引=%2, 位置(%3, %4), 距离=%5")
//                    .arg(i)
//                    .arg(idx)
//                    .arg(obstaclePos[idx].x(), 0, 'f', 1)
//                    .arg(obstaclePos[idx].y(), 0, 'f', 1)
//                    .arg(obstacleDistances[idx], 0, 'f', 1);
    }

    // 创建排序后的障碍物信息向量
    QVector<QPointF> sortedObstaclePos;
    QVector<float> sortedObstacleRadius;
    QVector<QPointF> sortedObstacleVelocity;
    QVector<float> sortedObstacleDistances;

    // 只取最近的两个障碍物
//    qDebug() << "=== 选择的障碍物 ===";
    for (int i = 0; i < qMin(2, indices.size()); i++) {
       int idx = indices[i];

//       qDebug() << QString("选择障碍物%1: 索引=%2, 位置(%3, %4), 距离=%5")
//                   .arg(i)
//                   .arg(idx)
//                   .arg(obstaclePos[idx].x(), 0, 'f', 1)
//                   .arg(obstaclePos[idx].y(), 0, 'f', 1)
//                   .arg(obstacleDistances[idx], 0, 'f', 1);

       sortedObstaclePos.append(obstaclePos[idx]);
       sortedObstacleRadius.append(obstacleRadius[idx]);
       sortedObstacleVelocity.append(obstacleVelocity[idx]);
       sortedObstacleDistances.append(obstacleDistances[idx]);
    }

    // 1. 自身信息
    QVector<float> observation;
    observation.append(self.x / 1280.0f);
    observation.append(self.y / 800.0f);
    observation.append(self.vx / 50.0f);
    observation.append(self.vy / 50.0f);
    observation.append(self.hp / 100.0f);
//    qDebug() << "自身向量:" << observation.mid(0, 5)<<"uid:"<<uavId;

    // 2. 队友信息
    int off = observation.size();
    for (int i = 0; i < 2; i++) {
        if (i < friendlyPos.size()) {
            float relX = (friendlyPos[i].x() - selfPos.x()) / 1280.0f;
            float relY = (friendlyPos[i].y() - selfPos.y()) / 800.0f;
            observation.append(relX); observation.append(relY); observation.append(friendlyHp[i]);
        } else {
            observation.append(0.0f); observation.append(0.0f); observation.append(0.0f);
        }
    }
//    qDebug() << "队友向量:" << observation.mid(off, 6);

    // 3. 敌机信息
    off = observation.size();
    for (int i = 0; i < 3; i++) {
        if (i < enemyPos.size()) {
            float relX = (enemyPos[i].x() - selfPos.x()) / 1280.0f;
            float relY = (enemyPos[i].y() - selfPos.y()) / 800.0f;
            observation.append(relX); observation.append(relY); observation.append(enemyHp[i]);
        } else {
            observation.append(0.0f); observation.append(0.0f); observation.append(0.0f);
        }
    }
//    qDebug() << "敌机向量:" << observation.mid(off, 9);

    // 4. 目标向量
    float relX = 0, relY = 0;
    int nearestIdx = -1;
    float minDist = std::numeric_limits<float>::max();

    if (!enemyPos.isEmpty()) {
        for (int i = 0; i < enemyPos.size(); i++) {
            float dx = enemyPos[i].x() - selfPos.x();
            float dy = enemyPos[i].y() - selfPos.y();
            float dist = sqrt(dx*dx + dy*dy);
            if (dist < minDist) {minDist = dist; nearestIdx = i;}
        }
        relX = (enemyPos[nearestIdx].x()) / 1280.0f;
        relY = (enemyPos[nearestIdx].y()) / 800.0f;
        observation.append(relX); observation.append(relY);
    } else {
        observation.append(-1.0f); observation.append(-1.0f);
    }
//    qDebug() << "目标向量:" << relX << relY;

    // 5. 障碍物信息
    off = observation.size();
    for (int i = 0; i < 2; i++) {
        if (i < sortedObstaclePos.size()) {
            float relX = (sortedObstaclePos[i].x()) / 1280.0f;
            float relY = (sortedObstaclePos[i].y()) / 800.0f;
            float relVx = sortedObstacleVelocity[i].x() / 50.0f; // 归一化速度x分量
            float relVy = sortedObstacleVelocity[i].y() / 50.0f; // 归一化速度y分量

            observation.append(relX);
            observation.append(relY);
            observation.append(relVx);
            observation.append(relVy);
        } else {
            observation.append(0.0f); observation.append(0.0f);
            observation.append(0.0f); observation.append(0.0f);
        }
    }
//    qDebug() << "障碍向量:" << observation.mid(off, 8);

    // 6. id - 第31维
    if(uavId == "B1"){
        observation.append(1.0f);
    }else if(uavId == "B2"){
        observation.append(2.0f);
    }else{
        observation.append(3.0f);
    }
    needInference = true;
    const QVector<float>& last = lastObservation[uavId];

    // 检查敌方向量(11-19)和障碍物向量(22-29)是否有变化
    bool enemyChanged = false;
    bool obstacleChanged = false;

    // 检查敌方向量
    for (int i = 11; i < 20 && i < last.size(); i++) {
       if (qAbs(last[i] - observation[i]) > 0.001f) {
           enemyChanged = true;
           break;
       }
    }

    // 如果有障碍物且最近的障碍物距离小于等于250，则需要推理
    if (!sortedObstacleDistances.isEmpty() && sortedObstacleDistances[0] <= 250.0f) {
        obstacleChanged = true;
    }

    // 如果敌方和障碍物向量都没有变化，不需要重新推理
    if (!enemyChanged && !obstacleChanged) {
       needInference = false;
    }

    // 更新上一次的观察向量
    lastObservation[uavId] = observation;

    qDebug() << "向量构建完成，维度:" << observation.size() << (needInference ? "需要推理" : "不需要推理");
    appendLog(QString("策略2：无人机 %1 向量构建完成，维度:%2").arg(uavId).arg(observation.size())+(needInference ? "需要推理" : "不需要推理"));
    return observation;
}

void MainWindow::initializeStrategy2s()
{
    // 清理现有的Strategy2实例
    for (auto it = Strategy2s.begin(); it != Strategy2s.end(); ++it) {
        delete it.value();
    }
    Strategy2s.clear();

    // 只有策略2才加载MADDPG模型
    if (currentStrategy == 2) {
        // 为每个蓝方无人机创建模型实例
        QStringList blueUAVs = {"B1", "B2", "B3"};

        for (int i = 0; i < blueUAVs.size(); i++) {
            QString uavId = blueUAVs[i];
            int agentId = i; // 对应agent_0, agent_1, agent_2

            // 创建模型实例
            SO2 = new Strategy2(agentId, this);

            // 设置运动参数
            SO2->setMaxVelocity(50.0f); // 最大速度(像素/秒)
            SO2->setTimeStep(0.1f);     // 时间步长(秒)

            // 加载模型参数
            QString modelPath = QString(":/agent_%1_actor_inference.json").arg(agentId);
            bool success = SO2->loadModel(modelPath);

            if (success) {
                qDebug() << "无人机" << uavId << "模型加载成功";
                Strategy2s[uavId] = SO2;
            } else {
                qDebug() << "无人机" << uavId << "模型加载失败";
                delete SO2;
            }
        }
    } else {
        qDebug() << "策略" << currentStrategy << "暂未实现，不加载模型";
    }
}

// 初始化MADDPG
void MainWindow::initializeMADDPG() {
    if (!isMaddpgInitialized) {
        // 初始化UAV模型
        initializeStrategy2s();

        // 初始化
        QStringList blueUAVs = {"B1", "B2", "B3"};
        int dim = 31; // 维度为31
        for (const QString& uavId : blueUAVs) {
            lastObservation[uavId] = QVector<float>(dim, -1.0f);  // 用31个-1.0f初始化
        }

        isMaddpgInitialized = true;
        qDebug() << "MADDPG初始化完成";
        appendLog("策略2：MADDPG初始化完成");
    }
}
// 清理MADDPG
void MainWindow::cleanupMADDPG() {
    if (isMaddpgInitialized) {
        // 清理UAV模型
        for (auto it = Strategy2s.begin(); it != Strategy2s.end(); ++it) {
            delete it.value();
        }
        Strategy2s.clear();

        isMaddpgInitialized = false;
        qDebug() << "MADDPG资源已清理";
    }
}

// 计算雷云移动速度
void MainWindow::calculateCloudVelocities() {
    QDateTime currentTime = QDateTime::currentDateTime();

    // 遍历所有移动障碍物（雷云）
    for (auto it = movingObstacles.begin(); it != movingObstacles.end(); ++it) {
        const QString &cloudId = it.key();
        const ObstacleInfo &cloudInfo = it.value();

        // 当前位置
        QPointF currentPos(cloudInfo.x, cloudInfo.y);

        // 如果这个雷云之前没有记录，创建新记录
        if (!cloudVelocities.contains(cloudId)) {
            CloudVelocityInfo velocityInfo;
            velocityInfo.currentPosition = currentPos;
            velocityInfo.lastPosition = currentPos; // 初始时，上一位置与当前位置相同
            velocityInfo.velocity = QPointF(0, 0); // 初始速度为0
            velocityInfo.lastUpdateTime = currentTime;
            velocityInfo.hasValidVelocity = false;

            cloudVelocities[cloudId] = velocityInfo;
        } else {
            // 获取上一次记录的信息
            CloudVelocityInfo &velocityInfo = cloudVelocities[cloudId];

            // 计算时间差（毫秒）
            qint64 timeDiff = velocityInfo.lastUpdateTime.msecsTo(currentTime);

            // 如果时间差足够大（避免频繁更新导致的误差），更新速度
            if (timeDiff > 100) { // 至少100毫秒
                // 保存上一位置
                velocityInfo.lastPosition = velocityInfo.currentPosition;
                // 更新当前位置
                velocityInfo.currentPosition = currentPos;

                // 计算速度向量（像素/秒）
                float dx = currentPos.x() - velocityInfo.lastPosition.x();
                float dy = currentPos.y() - velocityInfo.lastPosition.y();

                // 转换为每秒速度
                float vx = dx / (timeDiff / 1000.0f);
                float vy = dy / (timeDiff / 1000.0f);

                velocityInfo.velocity = QPointF(vx, vy);
                velocityInfo.lastUpdateTime = currentTime;
                velocityInfo.hasValidVelocity = true;

                // 调试输出
                // qDebug() << "雷云" << cloudId << "速度:" << velocityInfo.velocity;
            }
        }
    }

    // 清理不再存在的雷云记录
    QStringList keysToRemove;
    for (auto it = cloudVelocities.begin(); it != cloudVelocities.end(); ++it) {
        if (!movingObstacles.contains(it.key())) {
            keysToRemove.append(it.key());
        }
    }

    for (const QString &key : keysToRemove) {
        cloudVelocities.remove(key);
    }
}

//void MainWindow::calculateTargetInfo(const QPointF &selfPos, QVector<QPointF> &enemyPos, float &targetDistance, float &targetAngle)    //构建向量计算角度
//{
//    // 默认值
//    targetDistance = 1000.0f; // 一个较大的默认值
//    targetAngle = 0.0f;

//    if (enemyPos.isEmpty()) {
//        return;
//    }

//    // 找到最近的敌方无人机
//    QPointF nearestEnemy = enemyPos[0];
//    float minDistance = QVector2D(nearestEnemy - selfPos).length();

//    for (int i = 1; i < enemyPos.size(); i++) {
//        float distance = QVector2D(enemyPos[i] - selfPos).length();
//        if (distance < minDistance) {
//            minDistance = distance;
//            nearestEnemy = enemyPos[i];
//        }
//    }

//    // 计算距离和角度
//    targetDistance = minDistance;

//    // 计算角度（弧度）
//    float dx = nearestEnemy.x() - selfPos.x();
//    float dy = nearestEnemy.y() - selfPos.y();
//    targetAngle = qAtan2(dy, dx);
//}

void MainWindow::setupLogSystem()
{
    // 设置日志文本框为只读
    ui->record->setReadOnly(true);

    // 设置字体
    QFont logFont("Microsoft YaHei", 9);
    ui->record->setFont(logFont);

    // 连接按钮信号
    connect(ui->filterLogButton, &QPushButton::clicked, this, &MainWindow::filterLog);
    connect(ui->clearLogButton, &QPushButton::clicked, this, &MainWindow::clearLog);
    connect(ui->logFilterInput, &QLineEdit::returnPressed, this, &MainWindow::filterLog);

    // 添加初始日志
    appendLog("日志系统已初始化");
}

void MainWindow::appendLog(const QString &message)
{
    // 添加时间戳
    QString timestamp = QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss] ");
    QString formattedMsg = timestamp + message;

    // 存储原始消息
    logMessages.append(formattedMsg);

    // 添加消息到文本框
    ui->record->append(formattedMsg);

    // 滚动到底部 - 使用QTextEdit的moveCursor方法
    ui->record->moveCursor(QTextCursor::End);
}

void MainWindow::filterLog()
{
    QString keyword = ui->logFilterInput->text().trimmed();

    // 清空当前显示
    ui->record->clear();

    // 如果关键词为空，显示所有日志
    if (keyword.isEmpty()) {
        for (const QString &msg : logMessages) {
            ui->record->append(msg);
        }
    } else {
        // 显示包含关键词的日志
        for (const QString &msg : logMessages) {
            if (msg.contains(keyword, Qt::CaseInsensitive)) {
                ui->record->append(msg);
            }
        }
    }
}

void MainWindow::clearLog()
{
    // 清空日志存储和显示
    logMessages.clear();
    ui->record->clear();

    // 添加清空提示
    appendLog("日志已清空");
}

void MainWindow::receiveLogMessage(const QString &message)
{
    // 直接调用私有的appendLog方法
    appendLog(message);
}

//*****************策略4 人工势场算法相关的槽函数****************
// 构建策略4使用的27维观察向量，包括障碍物信息
QVector<float> MainWindow::buildS04Vector()
{
    // 扩展为27维向量：前18维与原来一致，后9维为3个障碍物(山脉、雷达、雷云)的坐标和半径
    QVector<float> observation(27, 0.0f);

    // 前9个元素：我方无人机B1, B2, B3的栅格位置和血量
    QStringList blueUAVs = {"B1", "B2", "B3"};
    for (int i = 0; i < 3; ++i) {
        const QString &droneId = blueUAVs[i];
        int baseIndex = i * 3;

        if (dronesInfo.contains(droneId) && dronesInfo[droneId].hp > 0) {
            // 无人机存在且血量大于0
            observation[baseIndex] = dronesInfo[droneId].x;     // gridCol
            observation[baseIndex + 1] = dronesInfo[droneId].y; // gridRow
            observation[baseIndex + 2] = dronesInfo[droneId].hp;                     // hp
        } else {
            // 无人机坠机，用0补齐
            observation[baseIndex] = 0.0f;
            observation[baseIndex + 1] = 0.0f;
            observation[baseIndex + 2] = 0.0f;
        }
    }

    // 中间9个元素：敌方无人机R1, R2, R3的栅格位置和血量
    QStringList redUAVs = {"R1", "R2", "R3"};
    for (int i = 0; i < 3; ++i) {
        const QString &droneId = redUAVs[i];
        int baseIndex = 9 + i * 3;

        if (dronesInfo.contains(droneId) && dronesInfo[droneId].hp > 0) {
            // 当前探测到敌方无人机且血量大于0
            observation[baseIndex] = dronesInfo[droneId].x ;     // gridCol
            observation[baseIndex + 1] = dronesInfo[droneId].y ; // gridRow
            observation[baseIndex + 2] = dronesInfo[droneId].hp;
        } else {
            // 未探测到敌方无人机，设置为-1
            observation[baseIndex] = -1.0f;
            observation[baseIndex + 1] = -1.0f;
            observation[baseIndex + 2] = -1.0f;
        }
    }

    // 后9个元素：障碍物信息(山脉、雷达、雷云)的位置和半径
    // 每3个一组，x、y坐标和半径

    // 山脉信息 - 索引18-20
    bool foundMountain = false;
    for (auto it = staticObstacles.begin(); it != staticObstacles.end(); ++it) {
        if (it.value().type == "mountain") {
            observation[18] = it.value().x ; // x坐标
            observation[19] = it.value().y ; // y坐标
            observation[20] = it.value().r ; // 半径
            foundMountain = true;
            break; // 只考虑第一个山脉
        }
    }
    if (!foundMountain) {
        observation[18] = 0.0f;
        observation[19] = 0.0f;
        observation[20] = 0.0f;
    }

    // 雷达信息 - 索引21-23
    bool foundRadar = false;
    for (auto it = staticObstacles.begin(); it != staticObstacles.end(); ++it) {
        if (it.value().type == "radar") {
            observation[21] = it.value().x ; // x坐标
            observation[22] = it.value().y ; // y坐标
            observation[23] = it.value().r ; // 半径
            foundRadar = true;
            break; // 只考虑第一个雷达
        }
    }
    if (!foundRadar) {
        observation[21] = 0.0f;
        observation[22] = 0.0f;
        observation[23] = 0.0f;
    }

    // 雷云信息 - 索引24-26
    bool foundCloud = false;
    for (auto it = movingObstacles.begin(); it != movingObstacles.end(); ++it) {
        if (it.value().type == "cloud") {
            observation[24] = it.value().x ; // x坐标
            observation[25] = it.value().y ; // y坐标
            observation[26] = gridMap->CLOUD_RADIUS ; // 半径
            foundCloud = true;
            break; // 只考虑第一个雷云
        }
    }
    if (!foundCloud) {
        observation[24] = 0.0f;
        observation[25] = 0.0f;
        observation[26] = 0.0f;
    }

//    qDebug()<<"observation"<<observation;

    return observation;
}

// 修改cleanupMADDPG函数后添加
void MainWindow::cleanupStrategy4() {
    if (isStrategy4Initialized) {
        // 清理策略4资源
        delete SO4;
        SO4 = nullptr;
        isStrategy4Initialized = false;
        qDebug() << "策略4资源已清理";
    }
}

// 初始化策略4
void MainWindow::initializeStrategy4() {
    if (!isStrategy4Initialized) {
        // 初始化策略4
        SO4 = new Strategy4();
        logouput.start();
        isStrategy4Initialized = true;
        qDebug() << "策略4：人工势场法算法资源初始化完成";
        appendLog("策略4：人工势场法算法资源初始化完成！");
    }
}
