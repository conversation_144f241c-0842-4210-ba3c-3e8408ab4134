#include "Strategy2.h"

Strategy2::Strategy2(int id, QObject *parent) : QObject(parent),
    inputDims(0), fc1Dims(0), fc2Dims(0), outputDims(0), uavId(id) //  初始化
{
}

// 加载 Actor 模型参数 (从JSON文件)
bool Strategy2::loadModel(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Strategy2 (ID:" << uavId << ") - 无法打开模型文件:" << filePath;
        return false;
    }

    QByteArray jsonData = file.readAll();
    file.close();

    QJsonDocument doc = QJsonDocument::fromJson(jsonData);
    if (doc.isNull()) {
        qWarning() << "Strategy2 (ID:" << uavId << ") - 解析JSON失败:" << filePath;
        return false;
    }

    QJsonObject params = doc.object();

    // 加载fc1参数
    if (params.contains("fc1.weight")) {
        QJsonArray weightArray = params["fc1.weight"].toArray();
        fc1Dims = weightArray.size();
        if (fc1Dims > 0) {
            inputDims = weightArray[0].toArray().size();

            fc1_weight.resize(fc1Dims);
            for (int i = 0; i < fc1Dims; i++) {
                QJsonArray rowArray = weightArray[i].toArray();
                fc1_weight[i].resize(inputDims);
                for (int j = 0; j < inputDims; j++) {
                    fc1_weight[i][j] = rowArray[j].toDouble();
                }
            }
        }
    }

    if (params.contains("fc1.bias")) {
        QJsonArray biasArray = params["fc1.bias"].toArray();
        fc1_bias.resize(biasArray.size());
        for (int i = 0; i < biasArray.size(); i++) {
            fc1_bias[i] = biasArray[i].toDouble();
        }
    }

    // 加载fc2参数
    if (params.contains("fc2.weight")) {
        QJsonArray weightArray = params["fc2.weight"].toArray();
        fc2Dims = weightArray.size();

        fc2_weight.resize(fc2Dims);
        for (int i = 0; i < fc2Dims; i++) {
            QJsonArray rowArray = weightArray[i].toArray();
            fc2_weight[i].resize(fc1Dims); // fc2的输入是fc1的输出维度
            for (int j = 0; j < fc1Dims; j++) {
                fc2_weight[i][j] = rowArray[j].toDouble();
            }
        }
    }

    if (params.contains("fc2.bias")) {
        QJsonArray biasArray = params["fc2.bias"].toArray();
        fc2_bias.resize(biasArray.size());
        for (int i = 0; i < biasArray.size(); i++) {
            fc2_bias[i] = biasArray[i].toDouble();
        }
    }

    // 加载pi参数 (输出层)
    if (params.contains("pi.weight")) {
        QJsonArray weightArray = params["pi.weight"].toArray();
        outputDims = weightArray.size();

        pi_weight.resize(outputDims);
        for (int i = 0; i < outputDims; i++) {
            QJsonArray rowArray = weightArray[i].toArray();
            pi_weight[i].resize(fc2Dims); // pi的输入是fc2的输出维度
            for (int j = 0; j < fc2Dims; j++) {
                pi_weight[i][j] = rowArray[j].toDouble();
            }
        }
    }

    if (params.contains("pi.bias")) {
        QJsonArray biasArray = params["pi.bias"].toArray();
        pi_bias.resize(biasArray.size());
        for (int i = 0; i < biasArray.size(); i++) {
            pi_bias[i] = biasArray[i].toDouble();
        }
    }

    qDebug() << "Strategy2 (ID:" << uavId << ") - 模型加载成功，输入维度:" << inputDims
             << "隐藏层1:" << fc1Dims
             << "隐藏层2:" << fc2Dims
             << "输出维度:" << outputDims;

    return true;
}

// 执行 Actor 网络前向传播，获取归一化动作
QPointF Strategy2::inference(const QVector<float> &observation)
{
    if (inputDims == 0) { // 检查模型是否已成功加载并初始化维度
        qWarning() << "Strategy2 (ID:" << uavId << ") - 模型未加载或维度未初始化，无法执行推理。";
        return QPointF(0, 0);
    }
    if (observation.size() != inputDims) {
        qWarning() << "Strategy2 (ID:" << uavId << ") - 输入维度不匹配，期望:" << inputDims << "实际:" << observation.size();
        return QPointF(0, 0);
    }
    
    // 检查第31维是否为1
    if (observation.size() >= 31 && observation[30] > 0.5f) {
        return customAvoidanceAndTracking(observation);
    }
    
    // 第一层前向传播: x = leaky_relu(fc1(state))
    QVector<float> fc1_output(fc1Dims, 0.0f);
    for (int i = 0; i < fc1Dims; i++) {
        float sum = fc1_bias.size() > i ? fc1_bias[i] : 0.0f; // 安全访问
        for (int j = 0; j < inputDims; j++) {
            if (fc1_weight.size() > i && fc1_weight[i].size() > j) { // 安全访问
                 sum += fc1_weight[i][j] * observation[j];
            } else {
                qWarning() << "Strategy2 (ID:" << uavId << ") - 访问越界: fc1_weight[" << i << "][" << j << "]";
                return QPointF(0,0); // 提前返回错误
            }
        }
        fc1_output[i] = leakyReLU(sum);
    }

    // 第二层前向传播: x = leaky_relu(fc2(x))
    QVector<float> fc2_output(fc2Dims, 0.0f);
    for (int i = 0; i < fc2Dims; i++) {
        float sum = fc2_bias.size() > i ? fc2_bias[i] : 0.0f; // 安全访问
        for (int j = 0; j < fc1Dims; j++) {
             if (fc2_weight.size() > i && fc2_weight[i].size() > j) { // 安全访问
                sum += fc2_weight[i][j] * fc1_output[j];
            } else {
                qWarning() << "Strategy2 (ID:" << uavId << ") - 访问越界: fc2_weight[" << i << "][" << j << "]";
                return QPointF(0,0); // 提前返回错误
            }
        }
        fc2_output[i] = leakyReLU(sum);
    }

    // 输出层前向传播: pi = softsign(pi(x))
    QVector<float> pi_output(outputDims, 0.0f);
     if (outputDims > 0) { // 确保输出层已定义
        for (int i = 0; i < outputDims; i++) {
            float sum = pi_bias.size() > i ? pi_bias[i] : 0.0f; // 安全访问
            for (int j = 0; j < fc2Dims; j++) {
                if (pi_weight.size() > i && pi_weight[i].size() > j) { // 安全访问
                    sum += pi_weight[i][j] * fc2_output[j];
                } else {
                    qWarning() << "Strategy2 (ID:" << uavId << ") - 访问越界: pi_weight[" << i << "][" << j << "]";
                    return QPointF(0,0); // 提前返回错误
                }
            }
            pi_output[i] = softsign(sum);
        }
    } else {
        qWarning() << "Strategy2 (ID:" << uavId << ") - 输出层维度为0，无法计算动作。";
        return QPointF(0,0);
    }


    // softsign 的输出 ax 和 ay 已经在 (-1, 1) 区间内
    // 无需额外缩放即可满足 [-1, 1] 的归一化动作要求
    float ax = (outputDims > 0 && pi_output.size() > 0) ? pi_output[0] : 0.0f;
    float ay = (outputDims > 1 && pi_output.size() > 1) ? pi_output[1] : 0.0f;

    return QPointF(ax, ay);
}

// 自定义避障和追踪逻辑
QPointF Strategy2::customAvoidanceAndTracking(const QVector<float> &observation)
{
    // 获取自身位置和速度
    float selfX = observation[0] * 1280.0f; // 反归一化
    float selfY = observation[1] * 800.0f;  // 反归一化
    float selfVx = observation[2] * 50.0f;  // 反归一化
    float selfVy = observation[3] * 50.0f;  // 反归一化
    QPointF selfPos(selfX, selfY);
    QPointF selfVel(selfVx, selfVy);
    
    // 获取障碍物信息（索引22-29）
    QVector<QPointF> obstaclePos;
    QVector<float> obstacleRadius;
    QVector<float> obstacleDistances;
    
    // 第一个障碍物
    float obs1X = observation[22] * 1280.0f; // 反归一化
    float obs1Y = observation[23] * 800.0f;  // 反归一化
    float obs1Radius = 80.0f; // 障碍物半径固定为80
    
    // 第二个障碍物
    float obs2X = observation[26] * 1280.0f; // 反归一化
    float obs2Y = observation[27] * 800.0f;  // 反归一化
    float obs2Radius = 80.0f; // 障碍物半径固定为80
    
    // 只添加有效的障碍物（非零位置）
    if (obs1X != 0 || obs1Y != 0) {
        float dx = obs1X - selfX;
        float dy = obs1Y - selfY;
        float distance = qSqrt(dx*dx + dy*dy);

        obstaclePos.append(QPointF(obs1X, obs1Y));
        obstacleRadius.append(obs1Radius);
        obstacleDistances.append(distance);
    }

    if (obs2X != 0 || obs2Y != 0) {
        float dx = obs2X - selfX;
        float dy = obs2Y - selfY;
        float distance = qSqrt(dx*dx + dy*dy);

        obstaclePos.append(QPointF(obs2X, obs2Y));
        obstacleRadius.append(obs2Radius);
        obstacleDistances.append(distance);
    }
    
    // 获取最近敌方无人机位置（目标）
    float targetX = 0, targetY = 0;
    bool hasTarget = false;
    
    // 目标信息在索引20-21
    targetX = observation[20] * 1280.0f; // 反归一化
    targetY = observation[21] * 800.0f;
    
    if (targetX >= 0 && targetY >= 0) { // 不是初始化向量说明有目标
        hasTarget = true;
//        qDebug()<<"目标存在坐标为："<<targetX<<targetY;
    }

    // 计算当前移动方向（如果有速度）
    float currentAngle = 0;
    bool hasCurrentDirection = false;

    if (qAbs(selfVx) > 0.1f || qAbs(selfVy) > 0.1f) {
        currentAngle = qAtan2(selfVy, selfVx);
        if (currentAngle < 0) currentAngle += 2 * M_PI;
        hasCurrentDirection = true;
        qDebug() << QString("当前飞行方向: %1° (弧度: %2)").arg(qRadiansToDegrees(currentAngle), 0, 'f', 1).arg(currentAngle, 0, 'f', 3);
    }

    // 计算安全角度范围
    safeAngleRanges.append(QPair<float, float>(0, 2 * M_PI)); // 初始安全角度为整个圆

//    qDebug() << "=== 开始计算安全角度范围 ===";

    // 对每个障碍物计算不安全角度范围
    for (int i = 0; i < obstaclePos.size(); i++) {
        float dx = obstaclePos[i].x() - selfX;
        float dy = obstaclePos[i].y() - selfY;
        float distance = obstacleDistances[i];

        // 如果障碍物太远，忽略它
        if (distance > 250) continue;

        // 获取障碍物速度
        float obsVx = 0, obsVy = 0;
        if (i == 0) {
            obsVx = observation[24] * 50.0f; // 第一个障碍物速度x
            obsVy = observation[25] * 50.0f; // 第一个障碍物速度y
        } else if (i == 1) {
            obsVx = observation[28] * 50.0f; // 第二个障碍物速度x
            obsVy = observation[29] * 50.0f; // 第二个障碍物速度y
        }
        // 如果障碍物是静态的或相对速度很小，使用当前位置
        if (obsVx == 0 && obsVy == 0) {
            // 静态障碍物，计算障碍物的角度
            float obstacleAngle = qAtan2(dy, dx);
            if (obstacleAngle < 0) obstacleAngle += 2 * M_PI;

            // 计算角度范围 - 修复：添加边界检查
            float sinValue = (obstacleRadius[i]) / distance;
            if (sinValue > 1.0f) sinValue = 1.0f;  // 防止asin参数超出范围

            float angleRange = qAsin(sinValue);
            float minAngle = obstacleAngle - angleRange;
            float maxAngle = obstacleAngle + angleRange;

            // 标准化角度
            while (minAngle < 0) minAngle += 2 * M_PI;
            while (maxAngle >= 2 * M_PI) maxAngle -= 2 * M_PI;

            qDebug() << QString("静态障碍物%1: 角度=%2°, 不安全范围=[%3°, %4°]")
                        .arg(i)
                        .arg(qRadiansToDegrees(obstacleAngle), 0, 'f', 1)
                        .arg(qRadiansToDegrees(minAngle), 0, 'f', 1)
                        .arg(qRadiansToDegrees(maxAngle), 0, 'f', 1);

            // 更新安全角度范围（使用你原有的逻辑）
            updateSafeAngleRanges(safeAngleRanges, minAngle, maxAngle);
        } else{
            // 移动障碍物，使用简化的椭圆形状
            float baseRadius = obstacleRadius[i]; // 80
            float extensionDistance = 50.0f; // 可调整
            float safetyMargin = 0.0f;

            // 计算速度方向
            float speedAngle = qAtan2(obsVy, obsVx);
            if (speedAngle < 0) speedAngle += 2 * M_PI;

            // 计算椭圆的两个焦点位置
            float frontFocusX = obstaclePos[i].x() + extensionDistance * qCos(speedAngle);
            float frontFocusY = obstaclePos[i].y() + extensionDistance * qSin(speedAngle);
            float backFocusX = obstaclePos[i].x() - extensionDistance * qCos(speedAngle);
            float backFocusY = obstaclePos[i].y() - extensionDistance * qSin(speedAngle);

            // 计算前方危险区域（椭圆长轴方向）
            float frontDx = frontFocusX - selfX;
            float frontDy = frontFocusY - selfY;
            float frontDistance = qSqrt(frontDx * frontDx + frontDy * frontDy);
            float frontAngle = qAtan2(frontDy, frontDx);
            if (frontAngle < 0) frontAngle += 2 * M_PI;

            // 计算后方危险区域（圆形）
            float backDx = obstaclePos[i].x() - selfX;
            float backDy = obstaclePos[i].y() - selfY;
            float backDistance = qSqrt(backDx * backDx + backDy * backDy);
            float backAngle = qAtan2(backDy, backDx);
            if (backAngle < 0) backAngle += 2 * M_PI;

            // 计算综合的不安全角度范围
            float effectiveRadius = baseRadius + extensionDistance + safetyMargin;
            float sinValue = effectiveRadius / qMax(frontDistance, effectiveRadius);
            if (sinValue > 1.0f) sinValue = 1.0f;

            float angleRange = qAsin(sinValue);
            float minAngle = frontAngle - angleRange;
            float maxAngle = frontAngle + angleRange;

            // 标准化角度
            while (minAngle < 0) minAngle += 2 * M_PI;
            while (maxAngle >= 2 * M_PI) maxAngle -= 2 * M_PI;

            qDebug() << QString("移动障碍物%1: 速度方向=%2°, 不安全范围=[%3°, %4°]")
                        .arg(i)
                        .arg(qRadiansToDegrees(speedAngle), 0, 'f', 1)
                        .arg(qRadiansToDegrees(minAngle), 0, 'f', 1)
                        .arg(qRadiansToDegrees(maxAngle), 0, 'f', 1);

            updateSafeAngleRanges(safeAngleRanges, minAngle, maxAngle);
        }
    }

    // 调试输出最终安全角度范围
//    qDebug() << "=== 最终安全角度范围 ===";
    for (int i = 0; i < safeAngleRanges.size(); i++) {
        float startDeg = qRadiansToDegrees(safeAngleRanges[i].first);
        float endDeg = qRadiansToDegrees(safeAngleRanges[i].second);
//        qDebug() << QString("范围%1: [%2°, %3°] (弧度:[%4, %5])")
//                    .arg(i)
//                    .arg(startDeg, 0, 'f', 1)
//                    .arg(endDeg, 0, 'f', 1)
//                    .arg(safeAngleRanges[i].first, 0, 'f', 3)
//                    .arg(safeAngleRanges[i].second, 0, 'f', 3);
    }

    // 如果没有安全角度范围，使用整个圆作为安全范围
    if (safeAngleRanges.isEmpty()) {
        safeAngleRanges.append(QPair<float, float>(0, 2 * M_PI));
//        qDebug() << "警告：没有安全角度范围，使用整个圆";
    }

    // 计算目标角度（如果有目标）
    float targetAngle = 0;
    if (hasTarget) {
        float dx = targetX - selfX;
        float dy = targetY - selfY;
        targetAngle = qAtan2(dy, dx);
        if (targetAngle < 0) targetAngle += 2 * M_PI;
//        qDebug() << QString("目标角度: %1° (弧度: %2)").arg(qRadiansToDegrees(targetAngle), 0, 'f', 1).arg(targetAngle, 0, 'f', 3);
    }

    // 检查当前飞行方向是否在安全范围内
    bool currentDirectionInSafeRange = false;
    if (hasCurrentDirection) {
        for (const auto &range : safeAngleRanges) {
            if ((range.first <= currentAngle && currentAngle <= range.second) ||
                (range.first > range.second && (currentAngle >= range.first || currentAngle <= range.second))) {
                currentDirectionInSafeRange = true;
//                qDebug() << "当前飞行角度处于安全角度范围内！";
                break;
            }
        }
        if (!currentDirectionInSafeRange) {
//            qDebug() << "当前飞行角度不在安全范围内！";
        }
    }

    // 检查目标角度是否在安全范围内
    bool targetInSafeRange = false;
    if (hasTarget) {
        for (const auto &range : safeAngleRanges) {
            if ((range.first <= targetAngle && targetAngle <= range.second) ||
                (range.first > range.second && (targetAngle >= range.first || targetAngle <= range.second))) {
                targetInSafeRange = true;
//                qDebug() << "目标角度在安全范围内！";
                break;
            }
        }
        if (!targetInSafeRange) {
//            qDebug() << "目标角度不在安全范围内！";
        }
    }

    // 选择最终角度
    float finalAngle = 0;

    if (hasTarget) {
        // 有目标的情况
        if (targetInSafeRange) {
            // 目标在安全范围内，直接飞向目标
            finalAngle = targetAngle;
            qDebug() << "策略：目标在安全范围内，直接飞向目标";
        } else {
            // 目标不在安全范围内，选择离目标角度最近的安全角度
            float minAngleDiff = 2 * M_PI;
            float bestAngle = 0;

            for (const auto &range : safeAngleRanges) {
                // 计算目标角度到安全范围边界的最短距离
                float diff1 = qAbs(range.first - targetAngle);
                if (diff1 > M_PI) diff1 = 2 * M_PI - diff1;

                float diff2 = qAbs(range.second - targetAngle);
                if (diff2 > M_PI) diff2 = 2 * M_PI - diff2;

                // 选择最接近目标角度的边界点
                if (diff1 < minAngleDiff) {
                    minAngleDiff = diff1;
                    bestAngle = range.first;
                }

                if (diff2 < minAngleDiff) {
                    minAngleDiff = diff2;
                    bestAngle = range.second;
                }
            }

            finalAngle = bestAngle;
            qDebug() << QString("策略：目标不在安全范围内，选择最接近目标角度的安全角度 %1°")
                        .arg(qRadiansToDegrees(finalAngle), 0, 'f', 1);
        }
    } else {
        // 没有目标的情况
        if (hasCurrentDirection && currentDirectionInSafeRange) {
            // 当前方向安全，保持当前方向
            finalAngle = currentAngle;
            qDebug() << "策略：无目标，当前方向安全，保持当前方向";
        } else {
            // 当前方向不安全或没有当前方向，使用搜索模式
            if (hasCurrentDirection) {
                // 有当前方向但不安全，选择最接近当前方向的安全角度
                float minAngleDiff = 2 * M_PI;
                float bestAngle = 0;

                for (const auto &range : safeAngleRanges) {
                    float diff1 = qAbs(range.first - currentAngle);
                    if (diff1 > M_PI) diff1 = 2 * M_PI - diff1;

                    float diff2 = qAbs(range.second - currentAngle);
                    if (diff2 > M_PI) diff2 = 2 * M_PI - diff2;

                    if (diff1 < minAngleDiff) {
                        minAngleDiff = diff1;
                        bestAngle = range.first;
                    }

                    if (diff2 < minAngleDiff) {
                        minAngleDiff = diff2;
                        bestAngle = range.second;
                    }
                }

                finalAngle = bestAngle;
                qDebug() << QString("策略：无目标，当前方向不安全，选择最接近当前方向的安全角度 %1°")
                            .arg(qRadiansToDegrees(finalAngle), 0, 'f', 1);
            } else {
                // 没有当前方向，使用预设的搜索角度
                if (observation[30] == 1.0f) {
                    finalAngle = qDegreesToRadians(178.0f);  // 向左偏上
                    qDebug() << "策略：无目标无当前方向，UAV1 搜索角度 178°";
                } else if (observation[30] == 2.0f) {
                    finalAngle = qDegreesToRadians(209.0f);  // 向左上
                    qDebug() << "策略：无目标无当前方向，UAV2 搜索角度 209°";
                } else if (observation[30] == 3.0f) {
                    finalAngle = qDegreesToRadians(233.0f);  // 向上偏左
                    qDebug() << "策略：无目标无当前方向，UAV3 搜索角度 233°";
                } else {
                    // 选择最大安全范围的中心点作为默认角度
                    float maxRangeSize = 0;
                    int maxRangeIndex = 0;

                    for (int i = 0; i < safeAngleRanges.size(); i++) {
                        float rangeSize = safeAngleRanges[i].second - safeAngleRanges[i].first;
                        if (rangeSize < 0) rangeSize += 2 * M_PI;

                        if (rangeSize > maxRangeSize) {
                            maxRangeSize = rangeSize;
                            maxRangeIndex = i;
                        }
                    }

                    float centerAngle = (safeAngleRanges[maxRangeIndex].first + safeAngleRanges[maxRangeIndex].second) / 2;
                    if (safeAngleRanges[maxRangeIndex].first > safeAngleRanges[maxRangeIndex].second) {
                        centerAngle += M_PI;
                        if (centerAngle > 2 * M_PI) centerAngle -= 2 * M_PI;
                    }

                    finalAngle = centerAngle;
                    qDebug() << QString("策略：无目标无当前方向，选择最大安全范围的中心点 %1°")
                                .arg(qRadiansToDegrees(finalAngle), 0, 'f', 1);
                }
            }
        }
    }

qDebug() << QString("最终选择角度: %1° (弧度: %2)").arg(qRadiansToDegrees(finalAngle), 0, 'f', 1).arg(finalAngle, 0, 'f', 3);
    
    // 根据最终角度计算速度分量
    float x = qCos(finalAngle);
    float y = qSin(finalAngle);
    float vx,vy;
    // 根据方向计算速度分量
    if (qAbs(x) > qAbs(y)) {
        // x方向分量更大
        vx = 50 * (x > 0 ? 1 : -1);
        vy = vx * (y / x);
    } else {
        // y方向分量更大
        vy = 50 * (y > 0 ? 1 : -1);
        vx = vy * (x / y);
    }
    
    return QPointF(vx, vy);
}

QPointF Strategy2::getTargetVelocity(const QVector<float> &observation)
{
    // 执行模型推理得到加速度方向分量
    QPointF acceleration_components = inference(observation);

    // 将加速度方向直接映射为速度方向
    float vx_dir = acceleration_components.x();
    float vy_dir = acceleration_components.y();

    float magnitude = qSqrt(vx_dir*vx_dir + vy_dir*vy_dir);
    if (magnitude > 1e-6f) { // 避免除以零或非常小的值
        // 将方向向量归一化，然后乘以最大速度
        return QPointF(-vx_dir / magnitude * vMax, -vy_dir / magnitude * vMax);
    }
    return QPointF(0, 0); // 如果输出接近零向量，则目标速度为零
}

QPointF Strategy2::getVelocity(const QVector<float> &observation)
{
    // 执行模型推理得到归一化的加速度分量
    QPointF normalized_accel = inference(observation);

    // 实际加速度值 (这里假设 normalized_accel 的每个分量直接代表了加速度的一个比例因子，
    // 乘以一个合适的标度。如果动作空间直接输出速度，则此逻辑不同)
    // 假设 timeStep 内加速度恒定，可以简单地认为是 timeStep 内速度的变化量与最大速度的比例。
    // 或者，可以定义一个 uav_accel_scale, 如Python环境中的 uav_input_accel_scale。
    // 为简单起见，这里假设 inference() 的输出直接按比例影响速度变化，并受到 timeStep 影响。
    // 这个转换逻辑需要与Python环境中的动作解释方式严格对应。
    // 此处的示例将归一化动作视为加速度与最大加速度的比例。
    // float accel_scale_factor = 10.0f; // 假设一个加速度标度，需要调整

    // 更新速度: v_new = v_current + a * dt
    // 这里的 a 是实际的加速度。如果 inference() 输出的是一个与最大加速度的比例，
    // 则 a = normalized_accel * max_physical_acceleration。
    // 为了简化，我们暂时不引入 max_physical_acceleration，而是让动作直接影响速度变化，
    // 并通过 vMax 进行裁剪。这种解释方式可能需要根据实际物理模型调整。

    float vx = currentVelocity.x() + normalized_accel.x() * vMax * timeStep; // 简化的模型，normalized_accel 影响vMax的一部分
    float vy = currentVelocity.y() + normalized_accel.y() * vMax * timeStep; // 简化的模型

    // 限制速度大小
    float magnitude = qSqrt(vx*vx + vy*vy);
    if (magnitude > vMax) {
        vx = vx / magnitude * vMax;
        vy = vy / magnitude * vMax;
    }

    currentVelocity = QPointF(vx, vy);
    return currentVelocity;
}

// 智能适应策略：评估场景复杂度并选择最优策略
int Strategy2::selectOptimalStrategy(const QVector<float> &observation)
{
    // 计算环境复杂度指标
    float obstacleComplexity = 0.0f;
    float enemyComplexity = 0.0f;

    // 障碍物复杂度评估
    int obstacleCount = 0;

    // 检查山体障碍物
    if (observation[21] > 0 && observation[22] > 0) {
        obstacleComplexity += 3.0f;
        obstacleCount++;
    }

    // 检查雷达障碍物
    if (observation[23] > 0 && observation[24] > 0) {
        obstacleComplexity += 2.0f;
        obstacleCount++;
    }

    // 检查雷云障碍物
    if (observation[25] > 0 && observation[26] > 0) {
        obstacleComplexity += 2.5f;  // 雷云移动，复杂度更高
        obstacleCount++;
    }

    // 多障碍物协同复杂度
    if (obstacleCount >= 2) {
        obstacleComplexity += obstacleCount * 0.5f;
    }

    // 敌机行为复杂度评估
    int activeEnemyCount = 0;
    for (int i = 0; i < 3; i++) {
        int baseIndex = 9 + i * 3;
        if (observation[baseIndex] > 0 && observation[baseIndex + 1] > 0 && observation[baseIndex + 2] > 0) {
            activeEnemyCount++;

            // 敌机血量影响复杂度（血量高的敌机更难对付）
            float enemyHp = observation[baseIndex + 2];
            enemyComplexity += enemyHp / 20.0f;  // 归一化血量影响
        }
    }

    // 总复杂度计算
    float totalComplexity = obstacleComplexity + enemyComplexity;

    // 策略选择逻辑
    if (totalComplexity <= 3.0f) {
        // 简单场景：使用规则策略（计算快速）
        return 0;
    } else if (totalComplexity <= 7.0f) {
        // 中等复杂场景：混合策略（根据具体情况选择）
        // 如果障碍物多，用RL；如果敌机多，用规则
        return (obstacleComplexity > enemyComplexity) ? 1 : 0;
    } else {
        // 高复杂场景：使用RL策略（智能决策）
        return 1;
    }
}

// 智能适应策略：性能监控和自适应调整
void Strategy2::adaptivePerformanceMonitoring(const QVector<float> &observation, const QPointF &lastAction)
{
    static QMap<QString, QVector<float>> performanceHistory;
    static QMap<QString, int> strategyUsageCount;

    // 获取当前无人机ID（这里简化处理，实际应该传入）
    QString currentUAV = "B1";  // 简化处理

    // 计算性能指标
    float currentHp = observation[2];  // 假设是B1的血量
    float distanceToNearestEnemy = std::numeric_limits<float>::max();

    // 计算到最近敌机的距离
    QPointF currentPos(observation[0], observation[1]);
    for (int i = 0; i < 3; i++) {
        int baseIndex = 9 + i * 3;
        if (observation[baseIndex] > 0 && observation[baseIndex + 1] > 0 && observation[baseIndex + 2] > 0) {
            QPointF enemyPos(observation[baseIndex], observation[baseIndex + 1]);
            float distance = QLineF(currentPos, enemyPos).length();
            distanceToNearestEnemy = qMin(distanceToNearestEnemy, distance);
        }
    }

    // 性能评分（血量保持 + 接敌效果）
    float performanceScore = currentHp / 100.0f;  // 血量保持分数
    if (distanceToNearestEnemy < std::numeric_limits<float>::max()) {
        performanceScore += qMax(0.0f, (500.0f - distanceToNearestEnemy) / 500.0f);  // 接敌效果分数
    }

    // 记录性能历史
    if (!performanceHistory.contains(currentUAV)) {
        performanceHistory[currentUAV] = QVector<float>();
        strategyUsageCount[currentUAV] = 0;
    }

    performanceHistory[currentUAV].append(performanceScore);
    if (performanceHistory[currentUAV].size() > 20) {  // 保持最近20次记录
        performanceHistory[currentUAV].removeFirst();
    }

    strategyUsageCount[currentUAV]++;

    // 每50次决策进行一次性能分析
    if (strategyUsageCount[currentUAV] % 50 == 0) {
        float avgPerformance = 0.0f;
        for (float score : performanceHistory[currentUAV]) {
            avgPerformance += score;
        }
        avgPerformance /= performanceHistory[currentUAV].size();

        // 根据性能调整策略偏好（这里可以实现学习机制）
        // qDebug() << "Strategy2: 无人机" << currentUAV << "平均性能:" << avgPerformance;
    }
}

float Strategy2::leakyReLU(float x)
{
    return x > 0 ? x : 0.01f * x;
}

float Strategy2::softsign(float x)
{
    return x / (1.0f + qAbs(x));
}

void Strategy2::updateSafeAngleRanges(QVector<QPair<float, float>>& safeAngleRanges, float minAngle, float maxAngle) {
    QVector<QPair<float, float>> newSafeRanges;

    for (const auto &range : safeAngleRanges) {
        // 处理跨越0/2π边界的情况
        if (minAngle > maxAngle) {
            // 障碍物角度范围跨越了0/2π边界
            if (range.first >= maxAngle && range.second <= minAngle) {
                // 安全范围完全在两个不安全区域之间
                newSafeRanges.append(range);
            } else {
                // 需要切割安全范围
                if (range.first < maxAngle && range.second > maxAngle) {
                    if (range.second <= minAngle) {
                        newSafeRanges.append(QPair<float, float>(maxAngle, range.second));
                    } else {
                        newSafeRanges.append(QPair<float, float>(maxAngle, minAngle));
                    }
                }
                if (range.first < minAngle && range.second > minAngle) {
                    if (range.first >= maxAngle) {
                        newSafeRanges.append(QPair<float, float>(range.first, minAngle));
                    } else {
                        newSafeRanges.append(QPair<float, float>(maxAngle, minAngle));
                    }
                }
            }
        } else {
            // 障碍物角度范围不跨越0/2π边界
            if (range.second <= minAngle || range.first >= maxAngle) {
                // 安全范围完全不与障碍物范围重叠
                newSafeRanges.append(range);
            } else {
                // 有重叠，需要分割
                if (range.first < minAngle) {
                    newSafeRanges.append(QPair<float, float>(range.first, minAngle));
                }
                if (range.second > maxAngle) {
                    newSafeRanges.append(QPair<float, float>(maxAngle, range.second));
                }
            }
        }
    }
    safeAngleRanges = newSafeRanges;
}
