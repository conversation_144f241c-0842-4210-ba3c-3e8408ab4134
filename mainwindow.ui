<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1921</width>
    <height>866</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>1921</width>
     <height>820</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>1921</width>
     <height>820</height>
    </size>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="1,4">
    <property name="spacing">
     <number>2</number>
    </property>
    <property name="sizeConstraint">
     <enum>QLayout::SetDefaultConstraint</enum>
    </property>
    <property name="leftMargin">
     <number>2</number>
    </property>
    <property name="topMargin">
     <number>2</number>
    </property>
    <property name="rightMargin">
     <number>2</number>
    </property>
    <property name="bottomMargin">
     <number>2</number>
    </property>
    <item>
     <layout class="QVBoxLayout" name="match_layout" stretch="360,100,60,260,25">
      <property name="spacing">
       <number>0</number>
      </property>
      <item>
       <widget class="QGroupBox" name="groupBox_3">
        <property name="minimumSize">
         <size>
          <width>641</width>
          <height>361</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>641</width>
          <height>361</height>
         </size>
        </property>
        <property name="title">
         <string>Status:</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="2,1">
         <property name="spacing">
          <number>1</number>
         </property>
         <property name="leftMargin">
          <number>1</number>
         </property>
         <property name="rightMargin">
          <number>1</number>
         </property>
         <item>
          <widget class="QGroupBox" name="groupBox">
           <property name="minimumSize">
            <size>
             <width>355</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>355</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="title">
            <string>蓝方无人机状态（实时更新）：</string>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <property name="spacing">
             <number>7</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout" stretch="20,24,110,88,96">
              <property name="spacing">
               <number>2</number>
              </property>
              <property name="leftMargin">
               <number>8</number>
              </property>
              <property name="rightMargin">
               <number>8</number>
              </property>
              <item>
               <widget class="QLabel" name="Bstat1">
                <property name="minimumSize">
                 <size>
                  <width>20</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>20</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>无</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="B1">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>2</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>24</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>24</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>B1:</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QProgressBar" name="Bhp1">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>110</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>110</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="value">
                 <number>100</number>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="Bponit1">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>88</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>88</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>(1280,800)</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="Bv1">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>96</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>96</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>vx:00,vy:00</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="20,24,110,88,96">
              <property name="spacing">
               <number>2</number>
              </property>
              <property name="leftMargin">
               <number>8</number>
              </property>
              <property name="rightMargin">
               <number>8</number>
              </property>
              <item>
               <widget class="QLabel" name="Bstat2">
                <property name="minimumSize">
                 <size>
                  <width>20</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>20</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>无</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="B2">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>24</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>24</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>B2:</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QProgressBar" name="Bhp2">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>110</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>110</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="value">
                 <number>100</number>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="Bponit2">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>88</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>88</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="contextMenuPolicy">
                 <enum>Qt::DefaultContextMenu</enum>
                </property>
                <property name="text">
                 <string>(1280,800)</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="Bv1_2">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>96</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>96</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>vx:00,vy:00</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="20,24,110,88,96">
              <property name="spacing">
               <number>2</number>
              </property>
              <property name="leftMargin">
               <number>8</number>
              </property>
              <property name="rightMargin">
               <number>8</number>
              </property>
              <item>
               <widget class="QLabel" name="Bstat3">
                <property name="minimumSize">
                 <size>
                  <width>20</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>20</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>无</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="B3">
                <property name="minimumSize">
                 <size>
                  <width>24</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>24</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>B3:</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QProgressBar" name="Bhp3">
                <property name="minimumSize">
                 <size>
                  <width>110</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>110</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="value">
                 <number>100</number>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="Bponit3">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>88</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>88</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>(1280,800)</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="Bv1_3">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>96</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>96</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>vx:00,vy:00</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_2">
           <property name="minimumSize">
            <size>
             <width>265</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>265</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="title">
            <string>红方无人机状态：</string>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <property name="leftMargin">
             <number>8</number>
            </property>
            <property name="rightMargin">
             <number>4</number>
            </property>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout" stretch="1,2">
              <property name="spacing">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="updataetime1">
                <property name="minimumSize">
                 <size>
                  <width>240</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>240</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>未更新</string>
                </property>
               </widget>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="20,24,110,88">
                <property name="spacing">
                 <number>2</number>
                </property>
                <item>
                 <widget class="QLabel" name="Rstat1">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>无</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="R1">
                  <property name="minimumSize">
                   <size>
                    <width>24</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>24</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>R1:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QProgressBar" name="Rhp1">
                  <property name="minimumSize">
                   <size>
                    <width>110</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>110</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="value">
                   <number>0</number>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="Rponit1">
                  <property name="minimumSize">
                   <size>
                    <width>88</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>88</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>unknown</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_2" stretch="1,2">
              <property name="spacing">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="updataetime2">
                <property name="minimumSize">
                 <size>
                  <width>240</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>240</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>未更新</string>
                </property>
               </widget>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="20,24,110,88">
                <property name="spacing">
                 <number>2</number>
                </property>
                <item>
                 <widget class="QLabel" name="Rstat2">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>无</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="R2">
                  <property name="minimumSize">
                   <size>
                    <width>24</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>24</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>R2:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QProgressBar" name="Rhp2">
                  <property name="minimumSize">
                   <size>
                    <width>110</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>110</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="value">
                   <number>0</number>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="Rponit2">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>88</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>88</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>unknown</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_3" stretch="1,2">
              <property name="spacing">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="updataetime3">
                <property name="minimumSize">
                 <size>
                  <width>240</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>240</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>未更新</string>
                </property>
               </widget>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="20,24,110,88">
                <property name="spacing">
                 <number>2</number>
                </property>
                <item>
                 <widget class="QLabel" name="Rstat3">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>无</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="R3">
                  <property name="minimumSize">
                   <size>
                    <width>24</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>24</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>R3:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QProgressBar" name="Rhp3">
                  <property name="minimumSize">
                   <size>
                    <width>110</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>110</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="value">
                   <number>0</number>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="Rponit3">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>88</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>88</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>unknown</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_4">
        <property name="minimumSize">
         <size>
          <width>640</width>
          <height>100</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>640</width>
          <height>100</height>
         </size>
        </property>
        <property name="title">
         <string>Strategy:</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="1,1,1,1,3">
         <property name="spacing">
          <number>40</number>
         </property>
         <property name="leftMargin">
          <number>40</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>40</number>
         </property>
         <property name="bottomMargin">
          <number>6</number>
         </property>
         <item>
          <widget class="QPushButton" name="SO1">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Maximum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>65</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>65</width>
             <height>50</height>
            </size>
           </property>
           <property name="text">
            <string>SO1</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="SO2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Maximum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>65</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>65</width>
             <height>50</height>
            </size>
           </property>
           <property name="text">
            <string>SO2</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="SO3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Maximum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>65</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>65</width>
             <height>50</height>
            </size>
           </property>
           <property name="text">
            <string>SO3</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="SO4">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Maximum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>65</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>65</width>
             <height>50</height>
            </size>
           </property>
           <property name="text">
            <string>SO4</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_11">
           <property name="spacing">
            <number>3</number>
           </property>
           <property name="bottomMargin">
            <number>4</number>
           </property>
           <item>
            <widget class="QLabel" name="win_icon">
             <property name="minimumSize">
              <size>
               <width>75</width>
               <height>75</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>75</width>
               <height>75</height>
              </size>
             </property>
             <property name="text">
              <string>胜率图标</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="win_rate">
             <property name="minimumSize">
              <size>
               <width>50</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>50</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>100%</string>
             </property>
             <property name="textFormat">
              <enum>Qt::RichText</enum>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="time_status">
        <property name="minimumSize">
         <size>
          <width>500</width>
          <height>60</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>500</width>
          <height>60</height>
         </size>
        </property>
        <property name="text">
         <string>游戏状态:                  剩余时间: </string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_6">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>260</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>260</height>
         </size>
        </property>
        <property name="title">
         <string>日志记录：</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_12">
         <property name="spacing">
          <number>10</number>
         </property>
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>10</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>10</number>
         </property>
         <item>
          <widget class="QTextEdit" name="record"/>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_13">
        <property name="spacing">
         <number>25</number>
        </property>
        <property name="leftMargin">
         <number>150</number>
        </property>
        <property name="rightMargin">
         <number>150</number>
        </property>
        <item>
         <widget class="QLineEdit" name="logFilterInput">
          <property name="minimumSize">
           <size>
            <width>120</width>
            <height>22</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>120</width>
            <height>22</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="filterLogButton">
          <property name="minimumSize">
           <size>
            <width>70</width>
            <height>25</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>70</width>
            <height>25</height>
           </size>
          </property>
          <property name="text">
           <string>查询</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="clearLogButton">
          <property name="minimumSize">
           <size>
            <width>70</width>
            <height>25</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>70</width>
            <height>25</height>
           </size>
          </property>
          <property name="sizeIncrement">
           <size>
            <width>0</width>
            <height>25</height>
           </size>
          </property>
          <property name="text">
           <string>清空</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QGroupBox" name="groupBox_5">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="title">
       <string>本地栅格地图：</string>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <property name="spacing">
        <number>2</number>
       </property>
       <property name="leftMargin">
        <number>2</number>
       </property>
       <property name="topMargin">
        <number>2</number>
       </property>
       <property name="rightMargin">
        <number>2</number>
       </property>
       <property name="bottomMargin">
        <number>2</number>
       </property>
       <item>
        <widget class="QWidget" name="gridmap" native="true">
         <property name="minimumSize">
          <size>
           <width>1280</width>
           <height>800</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>1280</width>
           <height>800</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1921</width>
     <height>23</height>
    </rect>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
