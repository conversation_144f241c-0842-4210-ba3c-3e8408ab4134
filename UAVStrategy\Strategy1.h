#ifndef STRATEGY1_H
#define STRATEGY1_H

#include <QDebug>
#include <QObject>
#include <QPoint>
#include <QPointF>
#include <QMap>
#include <QString>
#include <QVector>
#include <QDateTime>

class Strategy1 : public QObject
{
    Q_OBJECT
public:
    explicit Strategy1(QObject* parent = nullptr);

    // 设置追踪模式
    void setTrackingMode(bool isTracking);

    // 获取当前模式(追踪)
    bool isTrackingMode() const;

    // 更新敌方无人机位置信息
    void updateEnemyPositions(const QMap<QString, QPoint>& enemyPositions);

    // 更新敌方无人机位置和血量信息
    void updateEnemyInfo(const QMap<QString, QPoint>& enemyPositions, const QMap<QString, int>& enemyHp);

    // 为特定无人机单独设置追踪模式
    void setDroneTrackingMode(const QString& droneId, bool isTracking);

    // 获取特定无人机的追踪模式
    bool isDroneTracking(const QString& droneId) const;

    // 获取最优追踪目标点
    QPoint getBestTrackingTarget(const QString& droneId);

    // 闪电突击策略：快速计算最优速度向量
    QPointF calculateLightningStrikeVelocity(const QString& uavId, const QVector<float>& observation);

    // 闪电突击策略：选择最优突击目标（优先低血量敌机）
    int selectStrikeTarget(const QVector<float>& observation);

    // 闪电突击策略：计算高速直线攻击路径
    QPointF calculateDirectAttackPath(const QPointF& currentPos, const QPointF& targetPos);

signals:
    void logMessage(const QString &message); //日志输出

private:
    // 全局追踪模式标志
    bool m_isTrackingMode;

    // 单个无人机追踪模式标志
    QMap<QString, bool> m_droneTrackingMode;

    // 敌方无人机位置 - 键是敌方无人机ID，值是栅格坐标
    QMap<QString, QPoint> m_enemyPositions;

    // 敌方无人机血量 - 键是敌方无人机ID，值是血量
    QMap<QString, int> m_enemyHp;

    // 闪电突击策略：常量定义
    const float LIGHTNING_MAX_VELOCITY = 50.0f;    // 最大突击速度
    const float LIGHTNING_MIN_VELOCITY = 48.0f;    // 最小突击速度（保持高速）
    const float STRIKE_RANGE = 400.0f;             // 突击有效范围
    const float LOW_HP_THRESHOLD = 30.0f;          // 低血量阈值

    // 闪电突击策略：状态记录
    QMap<QString, QDateTime> m_lastStrikeTime;     // 上次突击时间
    QMap<QString, int> m_currentTarget;            // 当前目标索引
};

#endif // STRATEGY1_H
