<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>786</width>
    <height>485</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QWidget" name="controllerWidget" native="true">
    <property name="geometry">
     <rect>
      <x>130</x>
      <y>90</y>
      <width>563</width>
      <height>193</height>
     </rect>
    </property>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="leftMargin">
      <number>20</number>
     </property>
     <property name="rightMargin">
      <number>20</number>
     </property>
     <item>
      <widget class="QWidget" name="infowidget" native="true">
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0,0,2">
        <property name="spacing">
         <number>20</number>
        </property>
        <property name="leftMargin">
         <number>20</number>
        </property>
        <property name="rightMargin">
         <number>20</number>
        </property>
        <item>
         <widget class="QLabel" name="label">
          <property name="text">
           <string>UAV:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="id">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <item>
           <property name="text">
            <string>B1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>B2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>B3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>R1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>R2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>R3</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_3">
          <property name="text">
           <string>坐标:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="point">
          <property name="text">
           <string>point</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="widget_2" native="true">
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <item>
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>血量：</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QProgressBar" name="hp">
             <property name="value">
              <number>24</number>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>786</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
