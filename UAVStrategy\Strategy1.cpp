#include "Strategy1.h"

Strategy1::Strategy1(QObject* parent) : QObject(parent), m_isTrackingMode(false)
{
    // 初始化无人机追踪模式
    m_droneTrackingMode["B1"] = false;
    m_droneTrackingMode["B2"] = false;
    m_droneTrackingMode["B3"] = false;
}

// 设置追踪模式或巡逻模式
void Strategy1::setTrackingMode(bool isTracking)
{
    if (m_isTrackingMode != isTracking) {
        m_isTrackingMode = isTracking;
        QString logMsg = QString("策略1：设置全局模式为: %1").arg(isTracking ? "追踪模式" : "巡逻模式");
        emit logMessage(logMsg);

        // 同时更新所有无人机的模式
        QStringList droneIds = {"B1", "B2", "B3"};
        for (const QString& droneId : droneIds) {
            setDroneTrackingMode(droneId, isTracking);
        }
    }
}

// 获取当前模式(追踪或巡逻)
bool Strategy1::isTrackingMode() const
{
    return m_isTrackingMode;
}

// 更新敌方无人机位置信息
void Strategy1::updateEnemyPositions(const QMap<QString, QPoint>& enemyPositions)
{
    m_enemyPositions = enemyPositions;
}

// 更新敌方无人机位置和血量信息
void Strategy1::updateEnemyInfo(const QMap<QString, QPoint>& enemyPositions, const QMap<QString, int>& enemyHp)
{
    m_enemyPositions = enemyPositions;
    m_enemyHp = enemyHp;
}

// 为特定无人机单独设置追踪模式
void Strategy1::setDroneTrackingMode(const QString& droneId, bool isTracking)
{
    if (m_droneTrackingMode.contains(droneId) && m_droneTrackingMode[droneId] != isTracking) {
        m_droneTrackingMode[droneId] = isTracking;
        QString logMsg = QString("策略1：无人机 %1 设置为: %2").arg(droneId).arg(isTracking ? "追踪模式" : "巡逻模式");
        emit logMessage(logMsg);
    } else if (!m_droneTrackingMode.contains(droneId)) {
        m_droneTrackingMode[droneId] = isTracking;
    }
}

// 获取特定无人机的追踪模式
bool Strategy1::isDroneTracking(const QString& droneId) const
{
    return m_droneTrackingMode.value(droneId, false);
}

// 获取最优追踪目标点 - 优先选择血量高的敌方无人机，血量相同时优先选择R1>R2>R3
QPoint Strategy1::getBestTrackingTarget(const QString& droneId)
{
    QPoint bestTarget(0, 0);

    if (m_enemyPositions.isEmpty()) {
        return bestTarget;
    }

    // 创建一个记录敌方无人机ID、位置和血量的列表
    struct EnemyInfo {
        QString id;
        QPoint position;
        int hp;
    };

    QVector<EnemyInfo> enemies;

    // 收集所有敌方无人机信息
    for (auto it = m_enemyPositions.constBegin(); it != m_enemyPositions.constEnd(); ++it) {
        const QString& enemyId = it.key();
        if (enemyId.startsWith("R")) {
            EnemyInfo enemy;
            enemy.id = enemyId;
            enemy.position = it.value();
            enemy.hp = m_enemyHp.value(enemyId, 100); // 如果没有血量信息，默认为100
            enemies.append(enemy);
        }
    }

    // 如果没有敌方无人机，返回默认目标点
    if (enemies.isEmpty()) {
        return bestTarget;
    }

    // 对敌方无人机进行排序：首先按血量降序排序，然后按优先级排序(R1>R2>R3)
    std::sort(enemies.begin(), enemies.end(), [](const EnemyInfo& a, const EnemyInfo& b) {
        // 首先比较血量，血量高的优先
        if (a.hp != b.hp) {
            return a.hp > b.hp;
        }

        // 血量相同时，按R1>R2>R3的优先级
        return a.id < b.id; // R1排在R2和R3前面，因为字符串比较 "R1" < "R2" < "R3"
    });

    // 选择排序后的第一个敌方无人机作为目标
    bestTarget = enemies.first().position;

//    QString logMsg = QString("策略1：无人机 %1 选择 %2 作为追踪目标，血量:%3").arg(droneId).arg(enemies.first().id).arg(enemies.first().hp);
//    emit logMessage(logMsg);

    return bestTarget;
}

// 闪电突击策略：快速计算最优速度向量
QPointF Strategy1::calculateLightningStrikeVelocity(const QString& uavId, const QVector<float>& observation)
{
    // 解析当前无人机位置（前3个元素对应B1，中间3个对应B2，后3个对应B3）
    int uavIndex = uavId.mid(1).toInt() - 1;  // B1->0, B2->1, B3->2
    if (uavIndex < 0 || uavIndex >= 3) {
        return QPointF(0, 0);
    }

    int baseIndex = uavIndex * 3;
    QPointF currentPos(observation[baseIndex], observation[baseIndex + 1]);
    float currentHp = observation[baseIndex + 2];

    // 如果无人机已坠毁，返回零速度
    if (currentHp <= 0) {
        return QPointF(0, 0);
    }

    // 选择最优突击目标
    int targetIndex = selectStrikeTarget(observation);
    if (targetIndex < 0) {
        // 没有目标时，执行高速巡逻
        return QPointF(LIGHTNING_MAX_VELOCITY * 0.8f, 0);
    }

    // 获取目标位置
    int targetBaseIndex = 9 + targetIndex * 3;  // 红方无人机从索引9开始
    QPointF targetPos(observation[targetBaseIndex], observation[targetBaseIndex + 1]);

    // 计算高速直线攻击路径
    return calculateDirectAttackPath(currentPos, targetPos);
}

// 闪电突击策略：选择最优突击目标（优先低血量敌机）
int Strategy1::selectStrikeTarget(const QVector<float>& observation)
{
    int bestTarget = -1;
    float bestScore = -1;

    // 检查三个红方无人机（索引9-17）
    for (int i = 0; i < 3; i++) {
        int baseIndex = 9 + i * 3;

        // 检查敌机是否有效
        if (observation[baseIndex] < 0 || observation[baseIndex + 1] < 0 || observation[baseIndex + 2] <= 0) {
            continue;
        }

        float enemyHp = observation[baseIndex + 2];

        // 闪电突击策略：优先攻击低血量敌机（快速清理）
        float score = 100.0f - enemyHp;  // 血量越低分数越高

        // 额外奖励极低血量的敌机
        if (enemyHp <= LOW_HP_THRESHOLD) {
            score += 50.0f;
        }

        if (score > bestScore) {
            bestScore = score;
            bestTarget = i;
        }
    }

    return bestTarget;
}

// 闪电突击策略：计算高速直线攻击路径
QPointF Strategy1::calculateDirectAttackPath(const QPointF& currentPos, const QPointF& targetPos)
{
    // 计算方向向量
    QPointF direction = targetPos - currentPos;
    float distance = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());

    if (distance < 1.0f) {
        return QPointF(0, 0);
    }

    // 归一化方向向量
    direction = direction / distance;

    // 闪电突击：始终保持最高速度
    float velocity = LIGHTNING_MAX_VELOCITY;

    // 如果距离很近，稍微降低速度以提高精度
    if (distance < 100.0f) {
        velocity = LIGHTNING_MIN_VELOCITY;
    }

    return direction * velocity;
}
